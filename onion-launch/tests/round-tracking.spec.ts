import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell, Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { UserPurchase } from '../build/OnionAuction/OnionAuction_UserPurchase';
import { getSecureRandomBytes, keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('Round Tracking', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let user2: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let keyPair: any;
    let publicKeyBigInt: bigint;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user1 = await blockchain.treasury('user1');
        user2 = await blockchain.treasury('user2');

        // Generate key pair for signing
        const seed = await getSecureRandomBytes(32);
        keyPair = keyPairFromSeed(seed);
        publicKeyBigInt = BigInt('0x' + keyPair.publicKey.toString('hex'));

        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                toNano('1000'),
                toNano('10000'),
                toNano('1000000')
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Set signing key
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetSigningKey',
                public_key: publicKeyBigInt
            }
        );

        // Start the auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('1000'),
                hard_cap: toNano('10000'),
                initial_price: toNano('0.1')
            }
        );
    });

    // Helper function to create purchase calculation cell
    function createPurchaseCalculationCell(calc: any): Cell {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .storeCoins(calc.usdt_equivalent_amount)
            .endCell();
    }

    // Helper function to sign purchase calculation
    function signPurchaseCalculation(calc: any): Buffer {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    // Helper function to make a signature-based purchase
    async function makePurchaseWithSignature(user: SandboxContract<TreasuryContract>, amount: bigint, nonce: bigint) {
        const currentTime = Math.floor(Date.now() / 1000);
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n, // TON
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: nonce,
            usdt_equivalent_amount: amount // For TON payments, USDT equivalent equals the amount
        };

        const signature = signPurchaseCalculation(calculation);

        return await onionAuction.send(
            user.getSender(),
            { value: amount + toNano('0.2') }, // Amount + gas
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );
    }

    it('should initialize first round correctly', async () => {
        // Check current round
        const currentRound = await onionAuction.getCurrentRound();
        expect(currentRound).toBe(1n);

        // Check first round stats
        const round1Stats = await onionAuction.getRoundStats(1n);
        expect(round1Stats).toBeDefined();
        if (round1Stats) {
            expect(round1Stats.round_number).toBe(1n);
            expect(round1Stats.price).toBe(toNano('0.1'));
            expect(round1Stats.total_raised_ton).toBe(0n);
            expect(round1Stats.total_raised_usdt).toBe(0n);
            expect(round1Stats.tokens_sold).toBe(0n);
            expect(round1Stats.purchase_count).toBe(0n);
            expect(round1Stats.unique_users).toBe(0n);
            expect(round1Stats.refund_count).toBe(0n);
        }
    });

    it('should track purchases in round statistics', async () => {
        // User1 makes a purchase
        const purchaseResult1 = await makePurchaseWithSignature(user1, toNano('10'), 1n);

        expect(purchaseResult1.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Check round stats after first purchase
        const round1Stats = await onionAuction.getRoundStats(1n);
        expect(round1Stats).toBeDefined();
        if (round1Stats) {
            expect(round1Stats.total_raised_ton).toBe(toNano('10'));
            expect(round1Stats.tokens_sold).toBe(toNano('100')); // 10 TON / 0.1 TON per token
            expect(round1Stats.purchase_count).toBe(1n);
            expect(round1Stats.unique_users).toBe(1n);
        }

        // User2 makes a purchase
        const purchaseResult2 = await makePurchaseWithSignature(user2, toNano('5'), 2n);

        expect(purchaseResult2.transactions).toHaveTransaction({
            from: user2.address,
            to: onionAuction.address,
            success: true,
        });

        // Check round stats after second purchase
        const round1StatsUpdated = await onionAuction.getRoundStats(1n);
        expect(round1StatsUpdated).toBeDefined();
        if (round1StatsUpdated) {
            expect(round1StatsUpdated.total_raised_ton).toBe(toNano('15'));
            expect(round1StatsUpdated.tokens_sold).toBe(toNano('150')); // 15 TON / 0.1 TON per token
            expect(round1StatsUpdated.purchase_count).toBe(2n);
            expect(round1StatsUpdated.unique_users).toBe(2n);
        }

        // User1 makes another purchase (should not increase unique users)
        const purchaseResult3 = await makePurchaseWithSignature(user1, toNano('3'), 3n);

        expect(purchaseResult3.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Check round stats after third purchase
        const round1StatsFinal = await onionAuction.getRoundStats(1n);
        expect(round1StatsFinal).toBeDefined();
        if (round1StatsFinal) {
            expect(round1StatsFinal.total_raised_ton).toBe(toNano('18'));
            expect(round1StatsFinal.tokens_sold).toBe(toNano('180'));
            expect(round1StatsFinal.purchase_count).toBe(3n);
            expect(round1StatsFinal.unique_users).toBe(2n); // Still 2 unique users
        }
    });

    it('should track user participation across rounds', async () => {
        // User1 participates in round 1
        await makePurchaseWithSignature(user1, toNano('10'), 1n);

        // Check basic auction stats instead of user-specific round tracking
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised).toBe(toNano('10'));

        const totalTokensSold = await onionAuction.getTotalTokensSold();
        expect(totalTokensSold).toBe(toNano('100')); // 10 TON / 0.1 TON per token
    });

    it('should provide aggregated statistics', async () => {
        // Make purchases from different users
        await makePurchaseWithSignature(user1, toNano('10'), 1n);
        await makePurchaseWithSignature(user2, toNano('5'), 2n);

        // Get basic stats
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised).toBe(toNano('15'));

        const totalTokensSold = await onionAuction.getTotalTokensSold();
        expect(totalTokensSold).toBe(toNano('150')); // 15 TON / 0.1 TON per token
    });

    it('should track total rounds correctly', async () => {
        const totalRounds = await onionAuction.getTotalRounds();
        expect(totalRounds).toBe(1n); // Should be 1 since auction just started
    });

    it('should get current round stats', async () => {
        const currentRoundStats = await onionAuction.getCurrentRoundStats();
        expect(currentRoundStats).toBeDefined();
        if (currentRoundStats) {
            expect(currentRoundStats.round_number).toBe(1n);
            expect(currentRoundStats.price).toBe(toNano('0.1'));
        }
    });

    it('should track user purchases by round in UserPurchase contract', async () => {
        // Make a purchase
        await makePurchaseWithSignature(user1, toNano('10'), 1n);

        // Get user purchase contract
        const userPurchaseAddress = await onionAuction.getUserPurchaseAddress(user1.address);
        const userPurchase = blockchain.openContract(
            UserPurchase.fromAddress(userPurchaseAddress)
        );

        // Check purchase details
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeDefined();
        if (purchaseDetails) {
            expect(purchaseDetails.round_number).toBe(1n);
            expect(purchaseDetails.amount).toBe(toNano('10'));
            expect(purchaseDetails.currency).toBe(0n);
        }

        // Check round-specific stats
        const roundTotalAmount = await userPurchase.getRoundTotalAmount(1n);
        const roundTotalTokens = await userPurchase.getRoundTotalTokens(1n);
        const roundPurchaseCount = await userPurchase.getRoundPurchaseCount(1n);

        expect(roundTotalAmount).toBe(toNano('10'));
        expect(roundTotalTokens).toBe(toNano('100'));
        expect(roundPurchaseCount).toBe(1n);

        // Check participated rounds
        const participatedRounds = await userPurchase.getParticipatedRounds();
        expect(participatedRounds).toBeDefined();

        const roundCount = await userPurchase.getRoundCount();
        expect(roundCount).toBe(1n);
    });
});
