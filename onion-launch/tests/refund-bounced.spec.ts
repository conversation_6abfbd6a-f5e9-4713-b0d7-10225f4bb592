import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { UserPurchase } from '../build/OnionAuction/OnionAuction_UserPurchase';
import { getSecureRandomBytes, keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('Bounced Refund Mechanism', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let userPurchase: SandboxContract<UserPurchase>;
    let keyPair: any;
    let publicKeyBigInt: bigint;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user1 = await blockchain.treasury('user1');

        // Generate key pair for signing
        const seed = await getSecureRandomBytes(32);
        keyPair = keyPairFromSeed(seed);
        publicKeyBigInt = BigInt('0x' + keyPair.publicKey.toString('hex'));

        // Deploy auction contract
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(deployer.address, startTime, endTime, toNano('100'), toNano('1000'), toNano('10000'))
        );
        await onionAuction.send(deployer.getSender(), { value: toNano('1') }, 'Deploy');

        // Set signing key and start auction
        await onionAuction.send(deployer.getSender(), { value: toNano('0.1') }, { $$type: 'SetSigningKey', public_key: publicKeyBigInt });
        await onionAuction.send(
            deployer.getSender(), 
            { value: toNano('0.1') }, 
            { $$type: 'StartAuction', start_time: startTime, end_time: endTime, soft_cap: toNano('100'), hard_cap: toNano('1000'), initial_price: toNano('0.1') }
        );
        
        // Make a purchase to create the UserPurchase contract
        const purchaseAmount = toNano('10');
        const calculation = createCalculation(user1, purchaseAmount, 1n);
        const signature = signPurchaseCalculation(calculation);

        await onionAuction.send(user1.getSender(), { value: purchaseAmount + toNano('0.2') }, {
            $$type: 'PurchaseWithSignature',
            calculation: calculation,
            signature: beginCell().storeBuffer(signature).endCell().beginParse()
        });

        const userPurchaseAddress = await onionAuction.getUserPurchaseAddress(user1.address);
        userPurchase = blockchain.openContract(UserPurchase.fromAddress(userPurchaseAddress));
    });

    function createCalculation(user: SandboxContract<TreasuryContract>, amount: bigint, nonce: bigint, round: bigint = 1n) {
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;
        return {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount,
            currency: 0n,
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: round,
            timestamp: BigInt(Math.floor(Date.now() / 1000)),
            nonce,
            usdt_equivalent_amount: amount
        };
    }

    function createPurchaseCalculationCell(calc: any) {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .storeCoins(calc.usdt_equivalent_amount)
            .endCell();
    }

    function signPurchaseCalculation(calc: any) {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    it('should correctly revert refund status on bounce', async () => {
        // 1. Check initial state in UserPurchase contract
        let purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails?.amount).toEqual(toNano('10'));
        
        // 2. Drain the auction contract's balance so it can't afford the refund transaction
        // This simulates a scenario where the refund message would bounce.
        const auctionBalance = (await blockchain.getContract(onionAuction.address)).balance;
        await onionAuction.send(deployer.getSender(), { value: toNano('0.05') }, {
            $$type: 'WithdrawTON',
            amount: auctionBalance - toNano('0.01'), // Leave a tiny bit for gas
            destination: deployer.address
        });
        
        // 3. User requests a refund. This should succeed on UserPurchase's side for now.
        const refundRequestResult = await userPurchase.send(user1.getSender(), { value: toNano('0.1') }, {
            $$type: 'Refund',
            purchase_id: 1n
        });

        // The refund request marks the purchase as refunded immediately
        expect(await userPurchase.getIsRefunded(1n)).toBe(true);

        // 4. The `ProcessRefund` message from UserPurchase to OnionAuction now bounces.
        // The bounce handler in OnionAuction should send `RefundFailed` back to UserPurchase.
        // This is handled automatically by the sandbox emulator processing the transaction chain.
        
        // 5. Verify the refund status in UserPurchase is reverted.
        expect(await userPurchase.getIsRefunded(1n)).toBe(false);
        const finalPurchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(finalPurchaseDetails?.amount).toEqual(toNano('10'));

        const totalPaid = await userPurchase.getTotalPaid();
        expect(totalPaid).toEqual(toNano('10'));
    });

    it('should process refund successfully when auction has funds', async () => {
        // 1. Check initial state
        const initialUserBalance = (await blockchain.getContract(user1.address)).balance;
        expect(await userPurchase.getIsRefunded(1n)).toBe(false);

        // 2. Request refund (auction has enough balance)
        const refundRequestResult = await userPurchase.send(user1.getSender(), { value: toNano('0.1') }, {
            $$type: 'Refund',
            purchase_id: 1n
        });

        // 3. Verify UserPurchase state is updated
        expect(refundRequestResult.transactions).toHaveTransaction({
            from: user1.address,
            to: userPurchase.address,
            success: true
        });
        expect(await userPurchase.getIsRefunded(1n)).toBe(true);
        expect(await userPurchase.getTotalPaid()).toBe(0n);

        // 4. Verify user received the refund amount (minus fee)
        const finalUserBalance = (await blockchain.getContract(user1.address)).balance;
        const purchaseAmount = toNano('10');
        const fee = purchaseAmount * 5n / 100n;
        const expectedRefund = purchaseAmount - fee;
        
        // Check if the user's balance increased by approximately the refund amount.
        // It won't be exact due to gas fees.
        expect(finalUserBalance).toBeGreaterThan(initialUserBalance);
    });
});