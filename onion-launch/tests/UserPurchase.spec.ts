import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano } from '@ton/core';
import { UserPurchase } from '../build/OnionAuction/OnionAuction_UserPurchase';
import '@ton/test-utils';

describe('UserPurchase', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user: SandboxContract<TreasuryContract>;
    let auctionContract: SandboxContract<TreasuryContract>;
    let userPurchase: SandboxContract<UserPurchase>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user = await blockchain.treasury('user');
        auctionContract = await blockchain.treasury('auction');

        userPurchase = blockchain.openContract(
            await UserPurchase.fromInit(auctionContract.address, user.address)
        );

        const deployResult = await userPurchase.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: userPurchase.address,
            deploy: true,
            success: true,
        });
    });

    it('should deploy', async () => {
        // the check is done inside beforeEach
        // blockchain and userPurchase are ready to use
    });

    it('should create purchase record with direct method', async () => {
        const createPurchaseResult = await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n, // TON
                purchase_method: 0n, // Direct
                nonce: 0n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('100') // For TON purchases, USDT equivalent is the same as amount
            }
        );

        expect(createPurchaseResult.transactions).toHaveTransaction({
            from: auctionContract.address,
            to: userPurchase.address,
            success: true,
        });

        // Check purchase details
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeTruthy();
        expect(purchaseDetails!.purchase_method.toString()).toBe('0'); // Direct method
        expect(purchaseDetails!.nonce.toString()).toBe('0');
    });

    it('should create purchase record with signature verification method', async () => {
        const createPurchaseResult = await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n, // TON
                purchase_method: 1n, // Signature verified
                nonce: 12345n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('100') // For TON purchases, USDT equivalent is the same as amount
            }
        );

        expect(createPurchaseResult.transactions).toHaveTransaction({
            from: auctionContract.address,
            to: userPurchase.address,
            success: true,
        });

        // Check purchase details
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeTruthy();
        expect(purchaseDetails!.purchase_method.toString()).toBe('1'); // Signature verified method
        expect(purchaseDetails!.nonce.toString()).toBe('12345');
    });

    it('should track signature verified purchases count', async () => {
        // Create a direct purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('50'),
                tokens: toNano('500'),
                currency: 0n,
                purchase_method: 0n, // Direct
                nonce: 0n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('50') // For TON purchases, USDT equivalent is the same as amount
            }
        );

        // Create a signature verified purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n,
                purchase_method: 1n, // Signature verified
                nonce: 12345n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('100') // For TON purchases, USDT equivalent is the same as amount
            }
        );

        // Create another signature verified purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('200'),
                tokens: toNano('2000'),
                currency: 1n, // USDT
                purchase_method: 1n, // Signature verified
                nonce: 67890n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('200') // For USDT purchases, convert to USDT equivalent (200 USDT = 200 USDT)
            }
        );

        // Check signature verified purchases count
        const signatureVerifiedCount = await userPurchase.getSignatureVerifiedPurchases();
        expect(signatureVerifiedCount.toString()).toBe('2');

        // Check total purchase count
        const totalCount = await userPurchase.getPurchaseIdCounter();
        expect(totalCount.toString()).toBe('3');
    });

    it('should handle refund for signature verified purchase', async () => {
        // Create a signature verified purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n,
                purchase_method: 1n, // Signature verified
                nonce: 12345n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('100') // For TON purchases, USDT equivalent is the same as amount
            }
        );

        // Request refund
        const refundResult = await userPurchase.send(
            user.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'Refund',
                purchase_id: 1n
            }
        );

        expect(refundResult.transactions).toHaveTransaction({
            from: user.address,
            to: userPurchase.address,
            success: true,
        });

        // Check if refund was processed
        const isRefunded = await userPurchase.getIsRefunded(1n);
        expect(isRefunded).toBe(true);
    });

    it('should reject unauthorized purchase creation', async () => {
        const unauthorizedResult = await userPurchase.send(
            user.getSender(), // User trying to create purchase directly
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n,
                purchase_method: 1n,
                nonce: 12345n,
                round_number: 1n, // Assume round 1
                usdt_equivalent_amount: toNano('100') // For TON purchases, USDT equivalent is the same as amount
            }
        );

        expect(unauthorizedResult.transactions).toHaveTransaction({
            from: user.address,
            to: userPurchase.address,
            success: false,
        });
    });

    it('should correctly count participated rounds', async () => {
        // Round 1 purchase
        await userPurchase.send(auctionContract.getSender(), { value: toNano('0.1') }, {
            $$type: 'CreateUserPurchase', user: user.address, amount: toNano('10'), tokens: toNano('100'),
            currency: 0n, purchase_method: 0n, nonce: 0n, round_number: 1n, usdt_equivalent_amount: toNano('10')
        });

        // Round 3 purchase
        await userPurchase.send(auctionContract.getSender(), { value: toNano('0.1') }, {
            $$type: 'CreateUserPurchase', user: user.address, amount: toNano('20'), tokens: toNano('200'),
            currency: 0n, purchase_method: 0n, nonce: 0n, round_number: 3n, usdt_equivalent_amount: toNano('20')
        });

        // Round 1 again (should not increase count)
        await userPurchase.send(auctionContract.getSender(), { value: toNano('0.1') }, {
            $$type: 'CreateUserPurchase', user: user.address, amount: toNano('5'), tokens: toNano('50'),
            currency: 0n, purchase_method: 0n, nonce: 0n, round_number: 1n, usdt_equivalent_amount: toNano('5')
        });

        const roundCount = await userPurchase.getRoundCount();
        expect(roundCount).toBe(2n); // Participated in round 1 and 3

        // Round 5 purchase
        await userPurchase.send(auctionContract.getSender(), { value: toNano('0.1') }, {
            $$type: 'CreateUserPurchase', user: user.address, amount: toNano('30'), tokens: toNano('300'),
            currency: 0n, purchase_method: 0n, nonce: 0n, round_number: 5n, usdt_equivalent_amount: toNano('30')
        });

        const newRoundCount = await userPurchase.getRoundCount();
        expect(newRoundCount).toBe(3n);
    });
});
