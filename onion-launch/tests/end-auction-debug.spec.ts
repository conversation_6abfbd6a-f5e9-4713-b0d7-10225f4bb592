import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { getSecureRandomBytes, keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('End Auction Debug', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let keyPair: any;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user1 = await blockchain.treasury('user1');

        // Generate a key pair for signing
        const seed = await getSecureRandomBytes(32);
        keyPair = keyPairFromSeed(seed);

        const startTime = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
        const endTime = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                toNano('100'), // 100 tokens per round soft cap
                toNano('1000'), // 1000 tokens hard cap
                toNano('1000000') // 1M tokens total supply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            { $$type: 'Deploy', queryId: 0n }
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Set signing key
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetSigningKey',
                public_key: BigInt('0x' + keyPair.publicKey.toString('hex'))
            }
        );

        // Start auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'StartAuction',
                start_time: BigInt(startTime),
                end_time: BigInt(endTime),
                soft_cap: toNano('100'),
                hard_cap: toNano('1000'),
                initial_price: toNano('1')
            }
        );

        const auctionStatus = await onionAuction.getAuctionStatus();
        console.log('Auction status after start:', auctionStatus.toString());
    });

    // Helper function to create purchase calculation cell
    function createPurchaseCalculationCell(calc: any): Cell {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .storeCoins(calc.usdt_equivalent_amount)
            .endCell();
    }

    // Helper function to sign purchase calculation
    function signPurchaseCalculation(calc: any): Buffer {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    // Helper function to make a signature-based purchase
    async function makePurchaseWithSignature(user: SandboxContract<TreasuryContract>, amount: bigint, nonce: bigint) {
        const currentTime = Math.floor(Date.now() / 1000);
        const currentPrice = toNano('1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n, // TON
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: nonce,
            usdt_equivalent_amount: amount
        };

        const signature = signPurchaseCalculation(calculation);

        return await onionAuction.send(
            user.getSender(),
            { value: amount + toNano('0.1') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );
    }

    // Test removed - test_message is not a valid message type

    it('should end auction manually after successful purchases', async () => {
        // Make a purchase within round soft cap (10 TON = 100 tokens, exactly the round limit)
        const purchaseResult = await makePurchaseWithSignature(user1, toNano('10'), 1n);
        expect(purchaseResult.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Check status after purchase
        const statusAfterPurchase = await onionAuction.getAuctionStatus();
        console.log('Status after purchase:', statusAfterPurchase.toString());
        
        const totalTokensSold = await onionAuction.getTotalTokensSold();
        console.log('Total tokens sold:', totalTokensSold.toString());

        // Try to end auction
        const endResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        console.log('End auction transaction count:', endResult.transactions.length);
        
        // Check if end_auction transaction was successful
        expect(endResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check final status (should be 2 = ended_success since tokens were sold)
        const finalStatus = await onionAuction.getAuctionStatus();
        console.log('Final auction status:', finalStatus.toString());
        expect(finalStatus.toString()).toBe('2'); // ended_success
    });
});
