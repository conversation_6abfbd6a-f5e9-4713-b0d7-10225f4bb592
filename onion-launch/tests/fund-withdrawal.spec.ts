import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell, Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { getSecureRandomBytes, keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('Fund Withdrawal', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let treasury: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let keyPair: any;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        treasury = await blockchain.treasury('treasury');
        user1 = await blockchain.treasury('user1');

        // Generate key pair for signing
        const seed = await getSecureRandomBytes(32);
        keyPair = keyPairFromSeed(seed);
        const publicKeyBigInt = BigInt('0x' + keyPair.publicKey.toString('hex'));

        const startTime = BigInt(Math.floor(Date.now() / 1000) - 3600); // 1 hour ago
        const endTime = startTime + 7200n; // 2 hours after start (so it's already ended)

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                toNano('100'), // 100 tokens (minimum to sell for success)
                toNano('1000'), // 1000 tokens (maximum to sell, auction ends when reached)
                toNano('1000000') // 1M tokens total supply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );
        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Set signing key
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetSigningKey',
                public_key: publicKeyBigInt
            }
        );

        // Start the auction
        const startResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('100'),
                hard_cap: toNano('1000'),
                initial_price: toNano('1')
            }
        );

        // Check if start transaction was successful
        expect(startResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check if auction started successfully
        const statusAfterStart = await onionAuction.getAuctionStatus();
        console.log('Auction status after start:', statusAfterStart.toString());
    });

    // Helper function to create purchase calculation cell
    function createPurchaseCalculationCell(calc: any): Cell {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .storeCoins(calc.usdt_equivalent_amount)
            .endCell();
    }

    // Helper function to sign purchase calculation
    function signPurchaseCalculation(calc: any): Buffer {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    // Helper function to make a signature-based purchase
    async function makePurchaseWithSignature(user: SandboxContract<TreasuryContract>, amount: bigint, nonce: bigint) {
        const currentTime = Math.floor(Date.now() / 1000);
        const currentPrice = toNano('1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n, // TON
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: nonce,
            usdt_equivalent_amount: amount
        };

        const signature = signPurchaseCalculation(calculation);

        return await onionAuction.send(
            user.getSender(),
            { value: amount + toNano('0.2') }, // Amount + gas
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );
    }

    it('should set treasury address', async () => {
        const setTreasuryResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetTreasury',
                treasury_address: treasury.address
            }
        );

        expect(setTreasuryResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check treasury address was set
        const treasuryAddress = await onionAuction.getTreasuryAddress();
        expect(treasuryAddress?.toString()).toBe(treasury.address.toString());
    });

    it('should not allow withdrawal before auction ends successfully', async () => {
        // Make a purchase to have some funds
        await makePurchaseWithSignature(user1, toNano('10'), 1n);

        // Try to withdraw before auction ends
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('5'),
                destination: treasury.address
            }
        );

    });

    it('should allow withdrawal after successful auction', async () => {
        // Make enough purchases to reach soft cap (need 100+ tokens, so buy 110 tokens)
        const p1 = await makePurchaseWithSignature(user1, toNano('110'), 1n);
        expect(p1.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true
        });

        // Check auction status before ending
        const statusBefore = await onionAuction.getAuctionStatus();
        console.log('Auction status before ending:', statusBefore.toString());

        const totalRaised = await onionAuction.getTotalRaised();
        console.log('Total raised:', totalRaised.toString());

        const totalTokensSold = await onionAuction.getTotalTokensSold();
        console.log('Total tokens sold:', totalTokensSold.toString());

        const softCap = toNano('100'); // 100 tokens
        console.log('Soft cap (tokens):', softCap.toString());

        // Check auction status before ending
        const statusBeforeEnd = await onionAuction.getAuctionStatus();
        console.log('Auction status before end_auction call:', statusBeforeEnd.toString());

        // End the auction manually (simulate time passing)
        const endResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        console.log('End auction transaction count:', endResult.transactions.length);

        // Check if end_auction transaction was successful
        expect(endResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check auction status is successful (should be 2 = ended_success since we sold 110 tokens > 100 soft cap)
        const auctionStatus = await onionAuction.getAuctionStatus();
        console.log('Auction status after ending:', auctionStatus.toString());
        expect(auctionStatus.toString()).toBe('2'); // ended_success

        // Check withdrawable amounts
        const withdrawableTON = (await blockchain.getContract(onionAuction.address)).balance;
        console.log(withdrawableTON.toString())
        expect(withdrawableTON).toBeGreaterThan(110n); // 50 + 60 TON

        // Withdraw partial amount
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('50'),
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        expect(withdrawResult.transactions).toHaveTransaction({
            from: onionAuction.address,
            to: treasury.address,
            //value: toNano('50'),
        });

        // Check remaining withdrawable amount
        // const remainingWithdrawable = await onionAuction.getWithdrawableTon();
        // expect(remainingWithdrawable.toString()).toBe('60'); // 110 - 50
    });

    it('should allow withdrawing all funds at once', async () => {
        // Make purchases to reach soft cap
        await makePurchaseWithSignature(user1, toNano('120'), 1n);

        // End the auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        const endAuctionResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            'end_auction'
        );

        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: 0n, // Withdraw all
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        expect(withdrawResult.transactions).toHaveTransaction({
            from: onionAuction.address,
            to: treasury.address,
            // value: toNano('120'),
        });

        // Check no funds remaining
        const remainingWithdrawable = await onionAuction.getWithdrawableTon();
        expect(remainingWithdrawable.toString()).toBe('0');
    });

    it('should not allow non-owner to withdraw funds', async () => {
        // Make purchases and end auction
        await makePurchaseWithSignature(user1, toNano('120'), 1n);

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Try to withdraw as non-owner
        const withdrawResult = await onionAuction.send(
            user1.getSender(), // Non-owner
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('50'),
                destination: user1.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: false, // Should fail because user1 is not owner
        });
    });

    it('should not allow withdrawing more than available', async () => {
        // Make purchases and end auction
        await makePurchaseWithSignature(user1, toNano('120'), 1n);

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        // Try to withdraw more than available
        const withdrawResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'WithdrawTON',
                amount: toNano('200'), // More than the 120 TON available
                destination: treasury.address
            }
        );

        expect(withdrawResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: false, // Should fail due to insufficient balance
        });
    });

    it('should provide correct withdrawal summary', async () => {
        // Make purchases and end auction
        await makePurchaseWithSignature(user1, toNano('120'), 1n);

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            "end_auction"
        );

        const withdrawableTON = await onionAuction.getWithdrawableTon();
        expect(withdrawableTON.toString()).toBe('120000000000'); // 120 TON raised from purchase
    });
});
