import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano, Address, Cell } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import '@ton/test-utils';

describe('OnionAuction Events', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user = await blockchain.treasury('user');

        // Deploy OnionAuction
        const startTime = BigInt(Math.floor(Date.now() / 1000) + 60); // Start in 1 minute
        const endTime = startTime + 86400n; // End in 24 hours
        const softCap = toNano('100'); // 100 tokens (minimum to sell for success)
        const hardCap = toNano('1000'); // 1000 tokens (maximum to sell, auction ends when reached)
        const totalSupply = toNano('10000'); // 10k tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.5'),
            },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });
    });

    it('should emit AuctionStarted event when starting auction', async () => {
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        const softCap = toNano('100'); // 100 tokens
        const hardCap = toNano('1000'); // 1000 tokens
        const initialPrice = toNano('0.1');

        const result = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: softCap,
                hard_cap: hardCap,
                initial_price: initialPrice,
            }
        );

        expect(result.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check if the transaction contains an event (external message)
        const eventTx = result.transactions.find(tx => 
            tx.description.type === 'generic' && 
            tx.outMessages.size > 0
        );
        
        if (eventTx) {
            console.log('Event transaction found:', eventTx.outMessages);
        }
    });

    it('should emit USDTConfigured event when setting USDT address', async () => {
        const usdtMaster = Address.parse('EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs');
        const usdtWallet = Address.parse('kQAhz3Vorq3uIenecN3Bo8WRD5_hytdr7y2vPhE1D4h2BGKM');

        const result = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster,
                usdt_wallet: usdtWallet,
            }
        );

        expect(result.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check for event emission
        const eventTx = result.transactions.find(tx => 
            tx.description.type === 'generic' && 
            tx.outMessages.size > 0
        );
        
        if (eventTx) {
            console.log('USDT configured event transaction found:', eventTx.outMessages);
        }
    });

    it('should emit SigningKeySet event when setting signing key', async () => {
        const publicKey = BigInt('0x3f23bbe305ca208fd2d2bff11c503c1b4f53f676f1a5dd74d558ef478ee7ed7f');

        const result = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'SetSigningKey',
                public_key: publicKey,
            }
        );

        expect(result.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check for event emission
        const eventTx = result.transactions.find(tx => 
            tx.description.type === 'generic' && 
            tx.outMessages.size > 0
        );
        
        if (eventTx) {
            console.log('Signing key set event transaction found:', eventTx.outMessages);
        }
    });

    it('should emit MinPurchaseUpdated event when setting minimum purchase', async () => {
        const newMinPurchase = toNano('0.5');

        const result = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'SetMinPurchase',
                min_purchase: newMinPurchase,
            }
        );

        expect(result.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check for event emission
        const eventTx = result.transactions.find(tx => 
            tx.description.type === 'generic' && 
            tx.outMessages.size > 0
        );
        
        if (eventTx) {
            console.log('Min purchase updated event transaction found:', eventTx.outMessages);
        }
    });

    it('should emit TreasurySet event when setting treasury address', async () => {
        const newTreasury = user.address;

        const result = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'SetTreasury',
                treasury_address: newTreasury,
            }
        );

        expect(result.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check for event emission
        const eventTx = result.transactions.find(tx => 
            tx.description.type === 'generic' && 
            tx.outMessages.size > 0
        );
        
        if (eventTx) {
            console.log('Treasury set event transaction found:', eventTx.outMessages);
        }
    });

    it('should emit RoundUpdated event when updating round', async () => {
        // First start the auction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('100'), // 100 tokens
                hard_cap: toNano('1000'), // 1000 tokens
                initial_price: toNano('0.1'),
            }
        );

        // Then update round
        const result = await onionAuction.send(
            deployer.getSender(),
            {
                value: toNano('0.1'),
            },
            {
                $$type: 'UpdateRound',
                new_price: toNano('0.2'),
                round_number: 2n,
            }
        );

        expect(result.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check for event emission
        const eventTx = result.transactions.find(tx => 
            tx.description.type === 'generic' && 
            tx.outMessages.size > 0
        );
        
        if (eventTx) {
            console.log('Round updated event transaction found:', eventTx.outMessages);
        }
    });
});
