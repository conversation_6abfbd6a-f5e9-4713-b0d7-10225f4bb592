import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano, beginCell, Cell } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { getSecureRandomBytes, keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('OnionAuction Signature Verification', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user: SandboxContract<TreasuryContract>;
    let user2: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let keyPair: any;
    let publicKeyBigInt: bigint;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user = await blockchain.treasury('user');
        user2 = await blockchain.treasury('user2');

        // Generate key pair for signing
        const seed = await getSecureRandomBytes(32);
        keyPair = keyPairFromSeed(seed);
        publicKeyBigInt = BigInt('0x' + keyPair.publicKey.toString('hex'));

        // Create auction with proper parameters
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours later
        const softCap = toNano('500000'); // 500k tokens (maximum to sell per round)
        const hardCap = toNano('2000000'); // 2M tokens (maximum to sell, auction ends when reached)
        const totalSupply = toNano('1000000'); // 1M tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        // Deploy the contract
        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Set signing public key
        const setKeyResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetSigningKey',
                public_key: publicKeyBigInt
            }
        );

        expect(setKeyResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Start the auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: softCap,
                hard_cap: hardCap,
                initial_price: toNano('0.1')
            }
        );
    });

    // Helper function to create purchase calculation cell
    function createPurchaseCalculationCell(calc: any): Cell {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .storeCoins(calc.usdt_equivalent_amount)
            .endCell();
    }

    // Helper function to sign purchase calculation
    function signPurchaseCalculation(calc: any): Buffer {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    it('should set signing public key', async () => {
        const publicKey = await onionAuction.getSigningPublicKey();
        expect(publicKey.toString()).toBe(publicKeyBigInt.toString());
    });

    it('should verify signature verification is enabled', async () => {
        const isEnabled = await onionAuction.getIsSignatureVerificationEnabled();
        expect(isEnabled).toBe(true);
    });

    it('should handle purchase with valid signature', async () => {
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('100'); // 100 TON
        const currentPrice = toNano('0.1'); // 0.1 TON per token
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n, // TON
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: 1n,
            usdt_equivalent_amount: amount // For TON purchases, USDT equivalent is the same as amount
        };

        const signature = signPurchaseCalculation(calculation);

        const purchaseResult = await onionAuction.send(
            user.getSender(),
            { value: toNano('100.2') }, // Amount + gas
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: user.address,
            to: onionAuction.address,
            success: true,
        });

        // Check if nonce is marked as used
        const isNonceUsed = await onionAuction.getIsNonceUsed(1n);
        expect(isNonceUsed).toBe(true);

        // Check total tokens sold increased
        const totalTokensSold = await onionAuction.getTotalTokensSold();
        expect(totalTokensSold > 0n).toBe(true);
    });

    it('should reject purchase with invalid signature', async () => {
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('100');
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n,
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: 2n,
            usdt_equivalent_amount: amount // For TON purchases, USDT equivalent is the same as amount
        };

        // Create invalid signature (wrong data)
        const wrongCalculation = { ...calculation, amount: toNano('200') };
        const invalidSignature = signPurchaseCalculation(wrongCalculation);

        const purchaseResult = await onionAuction.send(
            user.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(invalidSignature).endCell().beginParse()
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: user.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should reject purchase with expired signature', async () => {
        const expiredTime = Math.floor(Date.now() / 1000) - 600; // 10 minutes ago
        const amount = toNano('100');
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n,
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(expiredTime),
            nonce: 3n,
            usdt_equivalent_amount: amount // For TON purchases, USDT equivalent is the same as amount
        };

        const signature = signPurchaseCalculation(calculation);

        const purchaseResult = await onionAuction.send(
            user.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: user.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should reject purchase with reused nonce', async () => {
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('100');
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: amount,
            currency: 0n,
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1n,
            timestamp: BigInt(currentTime),
            nonce: 4n,
            usdt_equivalent_amount: amount // For TON purchases, USDT equivalent is the same as amount
        };

        const signature = signPurchaseCalculation(calculation);

        // First purchase should succeed
        const firstPurchase = await onionAuction.send(
            user.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(firstPurchase.transactions).toHaveTransaction({
            from: user.address,
            to: onionAuction.address,
            success: true,
        });

        // Second purchase with same nonce should fail
        const secondPurchase = await onionAuction.send(
            user.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(secondPurchase.transactions).toHaveTransaction({
            from: user.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should enforce round soft cap limits', async () => {
        // First purchase: 400k tokens (within 500k soft cap)
        const calc1 = {
            $$type: 'PurchaseCalculation' as const,
            user: user.address,
            amount: toNano('40'), // 40 TON
            currency: 0n,
            tokens_to_receive: toNano('400000'), // 400k tokens
            current_price: toNano('0.1'),
            current_round: 1n,
            timestamp: BigInt(Math.floor(Date.now() / 1000)),
            nonce: 1n,
            usdt_equivalent_amount: toNano('40') * 1000n
        };

        const signature1 = signPurchaseCalculation(calc1);

        const purchase1 = await onionAuction.send(
            user.getSender(),
            { value: toNano('40') + toNano('0.1') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calc1,
                signature: beginCell().storeBuffer(signature1).endCell().asSlice()
            }
        );

        expect(purchase1.transactions).toHaveTransaction({
            from: user.address,
            to: onionAuction.address,
            success: true,
        });

        // Second purchase: 200k tokens (would exceed 500k soft cap)
        const calc2 = {
            $$type: 'PurchaseCalculation' as const,
            user: user2.address,
            amount: toNano('20'), // 20 TON
            currency: 0n,
            tokens_to_receive: toNano('200000'), // 200k tokens
            current_price: toNano('0.1'),
            current_round: 1n,
            timestamp: BigInt(Math.floor(Date.now() / 1000)),
            nonce: 2n,
            usdt_equivalent_amount: toNano('20') * 1000n
        };

        const signature2 = signPurchaseCalculation(calc2);

        const purchase2 = await onionAuction.send(
            user2.getSender(),
            { value: toNano('20') + toNano('0.1') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calc2,
                signature: beginCell().storeBuffer(signature2).endCell().asSlice()
            }
        );

        expect(purchase2.transactions).toHaveTransaction({
            from: user2.address,
            to: onionAuction.address,
            success: false,
            exitCode: 51022, // ERROR_ROUND_SOFT_CAP_EXCEEDED
        });
    });
});
