# 独立合约调用实现

## 🎯 目标

将链上 get method 调用从依赖合约编译结果的方式改为使用独立方法，以保证依赖的简单性，同时将结果解析成对象。

## ✅ 实现内容

### 1. 创建独立的合约工具模块

**文件**: `src/lib/contractUtils.ts`

提供了以下独立的工具函数：

```typescript
// 核心调用方法
callContractGetMethod(client, address, method, args) // 独立的合约调用
parsePurchaseDetails(result) // 解析购买记录结构

// 具体业务方法
getPurchaseCount(client, address) // 获取购买数量
getPurchaseDetails(client, address, id) // 获取购买详情
isRefunded(client, address, id) // 检查退款状态
getTotalPurchased(client, address) // 获取总购买量
getTotalPaid(client, address) // 获取总支付额
getSignatureVerifiedPurchases(client, address) // 获取签名验证购买数
```

### 2. 更新 useUserPurchases Hook

**文件**: `src/hooks/useUserPurchases.ts`

**主要变更**:
- 移除了对 `UserPurchase` 包装器的依赖
- 使用独立的工具函数调用合约方法
- 保持了相同的接口和功能

**重构前**:
```typescript
import { UserPurchase } from '../../../wrappers/UserPurchase'

const userPurchase = client.open(UserPurchase.createFromAddress(address))
const purchaseCount = await userPurchase.getPurchaseIdCounter()
const purchaseRecord = await userPurchase.getPurchaseDetails(i)
const isRefunded = await userPurchase.isRefunded(i)
```

**重构后**:
```typescript
import { getPurchaseCount, getPurchaseDetails, isRefunded } from '../lib/contractUtils'

const purchaseCount = await getPurchaseCount(client, address)
const purchaseRecord = await getPurchaseDetails(client, address, i)
const purchaseIsRefunded = await isRefunded(client, address, i)
```

## 🚀 优势

### 1. 依赖简化
- **之前**: 需要导入完整的合约包装器类
- **现在**: 只需要基础的 TON SDK 和独立工具函数

### 2. 更好的可维护性
- 独立的函数更容易测试
- 不依赖合约编译结果
- 可以独立更新和优化

### 3. 更清晰的职责分离
- 合约调用逻辑与业务逻辑分离
- 数据解析逻辑独立
- 更容易理解和调试

### 4. 更好的错误处理
- 每个函数都有独立的错误处理
- 更精确的错误信息
- 更容易定位问题

## 📖 使用示例

### 基本用法

```typescript
import { TonClient, Address } from '@ton/ton'
import { getPurchaseCount, getPurchaseDetails } from './lib/contractUtils'

const client = new TonClient({ ... })
const userPurchaseAddress = Address.parse('...')

// 获取购买数量
const count = await getPurchaseCount(client, userPurchaseAddress)

// 获取具体购买记录
for (let i = 1; i <= count; i++) {
  const purchase = await getPurchaseDetails(client, userPurchaseAddress, i)
  if (purchase) {
    console.log(`Purchase ${i}:`, {
      amount: purchase.amount.toString(),
      tokens: purchase.tokens.toString(),
      currency: purchase.currency === 0 ? 'TON' : 'USDT'
    })
  }
}
```

### 直接合约调用

```typescript
import { callContractGetMethod } from './lib/contractUtils'

// 调用任意合约方法
const result = await callContractGetMethod(
  client, 
  contractAddress, 
  'method_name', 
  [arg1, arg2]
)

// 手动解析结果
const value = result.stack.readNumber()
```

## 🧪 测试

创建了测试文件 `src/lib/contractUtils.test.ts` 来验证所有功能：

```bash
# 运行测试（如果在 Node.js 环境中）
node src/lib/contractUtils.test.ts
```

## 🔄 迁移指南

如果你有其他地方使用了合约包装器，可以按以下步骤迁移：

### 步骤 1: 导入工具函数
```typescript
import { getPurchaseDetails, isRefunded } from './lib/contractUtils'
```

### 步骤 2: 替换合约调用
```typescript
// 旧方式
const wrapper = client.open(UserPurchase.createFromAddress(address))
const result = await wrapper.getPurchaseDetails(id)

// 新方式
const result = await getPurchaseDetails(client, address, id)
```

### 步骤 3: 移除包装器导入
```typescript
// 删除这行
import { UserPurchase } from '../wrappers/UserPurchase'
```

## 📝 注意事项

1. **保持接口兼容**: 新的工具函数返回相同的数据结构
2. **错误处理**: 每个函数都包含适当的错误处理
3. **类型安全**: 使用 TypeScript 接口确保类型安全
4. **性能**: 独立调用可能比包装器稍快，因为减少了抽象层

## 🎉 总结

通过这次重构，我们实现了：
- ✅ 独立的合约调用方法
- ✅ 简化的依赖关系
- ✅ 更好的可维护性
- ✅ 保持了原有功能
- ✅ 提供了清晰的迁移路径

这种方式更符合函数式编程的理念，提供了更简单、更可靠的合约交互方式。
