/**
 * 独立合约调用示例
 * 
 * 这个文件演示了如何使用新的独立方法调用链上合约，
 * 而不依赖复杂的合约包装器。
 */

import { TonClient, Address, TupleBuilder } from '@ton/ton'

// 配置
const TESTNET_ENDPOINT = 'https://testnet.toncenter.com/api/v2/jsonRPC'
const API_KEY = process.env.NEXT_PUBLIC_TON_API_KEY

// 创建 TON 客户端
const client = new TonClient({
  endpoint: TESTNET_ENDPOINT,
  apiKey: API_KEY
})

/**
 * 独立的合约调用方法
 * 这个方法不依赖任何合约包装器，直接使用 TON SDK
 */
async function callContractMethod(contractAddress, methodName, args = []) {
  console.log(`📞 调用合约方法: ${methodName}`)
  console.log(`📍 合约地址: ${contractAddress}`)
  
  try {
    // 构建参数
    const argsBuilder = new TupleBuilder()
    args.forEach(arg => {
      if (typeof arg === 'number') {
        argsBuilder.writeNumber(arg)
      } else if (typeof arg === 'bigint') {
        argsBuilder.writeNumber(Number(arg))
      } else if (arg instanceof Address) {
        argsBuilder.writeAddress(arg)
      }
    })

    // 调用合约方法
    const result = await client.runMethod(
      Address.parse(contractAddress), 
      methodName, 
      argsBuilder.build()
    )

    console.log(`✅ 调用成功`)
    return result
  } catch (error) {
    console.error(`❌ 调用失败:`, error.message)
    throw error
  }
}

/**
 * 解析购买记录
 */
function parsePurchaseRecord(result) {
  try {
    if (result.stack.remaining === 0) {
      return null
    }

    const record = result.stack.readTuple()
    
    return {
      id: record.readNumber(),
      user: record.readAddress(),
      amount: record.readBigNumber(),
      tokens: record.readBigNumber(),
      timestamp: record.readNumber(),
      currency: record.readNumber(), // 0=TON, 1=USDT
      purchase_method: record.readNumber(), // 0=direct, 1=signature_verified
      nonce: record.readBigNumber()
    }
  } catch (error) {
    console.error('解析购买记录失败:', error)
    return null
  }
}

/**
 * 示例：获取用户购买数据
 */
async function getUserPurchaseData(userPurchaseAddress) {
  console.log('\n🔍 获取用户购买数据')
  console.log('===================')
  
  try {
    // 1. 获取购买数量
    console.log('\n1️⃣ 获取购买数量...')
    const countResult = await callContractMethod(userPurchaseAddress, 'purchase_id_counter')
    const purchaseCount = countResult.stack.readNumber()
    console.log(`📊 购买数量: ${purchaseCount}`)

    if (purchaseCount === 0) {
      console.log('ℹ️ 没有购买记录')
      return
    }

    // 2. 获取所有购买记录
    console.log('\n2️⃣ 获取购买记录...')
    const purchases = []
    
    for (let i = 1; i <= purchaseCount; i++) {
      try {
        // 获取购买详情
        const detailsResult = await callContractMethod(userPurchaseAddress, 'purchase_details', [i])
        const purchaseRecord = parsePurchaseRecord(detailsResult)
        
        if (purchaseRecord) {
          // 检查退款状态
          const refundResult = await callContractMethod(userPurchaseAddress, 'is_refunded', [i])
          const isRefunded = refundResult.stack.readBoolean()
          
          // 格式化数据
          const purchase = {
            id: purchaseRecord.id,
            amount: Number(purchaseRecord.amount) / 1000000000, // 转换为 TON 单位
            tokens: Number(purchaseRecord.tokens) / 1000000000, // 转换为代币单位
            currency: purchaseRecord.currency === 0 ? 'TON' : 'USDT',
            method: purchaseRecord.purchase_method === 0 ? 'direct' : 'signature_verified',
            timestamp: new Date(purchaseRecord.timestamp * 1000).toISOString(),
            isRefunded: isRefunded,
            nonce: purchaseRecord.nonce.toString()
          }
          
          purchases.push(purchase)
          
          console.log(`📝 购买记录 ${i}:`)
          console.log(`   💰 金额: ${purchase.amount} ${purchase.currency}`)
          console.log(`   🪙 代币: ${purchase.tokens}`)
          console.log(`   📅 时间: ${purchase.timestamp}`)
          console.log(`   🔧 方式: ${purchase.method}`)
          console.log(`   ↩️ 已退款: ${purchase.isRefunded}`)
        }
      } catch (error) {
        console.error(`❌ 获取购买记录 ${i} 失败:`, error.message)
      }
    }

    // 3. 获取汇总信息
    console.log('\n3️⃣ 获取汇总信息...')
    
    try {
      const totalPurchasedResult = await callContractMethod(userPurchaseAddress, 'total_purchased')
      const totalPurchased = totalPurchasedResult.stack.readBigNumber()
      console.log(`🎯 总购买代币: ${Number(totalPurchased) / 1000000000}`)
    } catch (error) {
      console.error('获取总购买量失败:', error.message)
    }

    try {
      const totalPaidResult = await callContractMethod(userPurchaseAddress, 'total_paid')
      const totalPaid = totalPaidResult.stack.readBigNumber()
      console.log(`💸 总支付金额: ${Number(totalPaid) / 1000000000}`)
    } catch (error) {
      console.error('获取总支付额失败:', error.message)
    }

    return purchases
    
  } catch (error) {
    console.error('💥 获取用户购买数据失败:', error)
    throw error
  }
}

/**
 * 主函数 - 演示独立调用
 */
async function demonstrateIndependentCalls() {
  console.log('🚀 独立合约调用演示')
  console.log('==================')
  
  // 示例地址（请替换为实际地址）
  const exampleUserPurchaseAddress = 'EQxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
  
  try {
    await getUserPurchaseData(exampleUserPurchaseAddress)
    console.log('\n🎉 演示完成！')
  } catch (error) {
    console.error('💥 演示失败:', error)
  }
}

/**
 * 对比：旧方式 vs 新方式
 */
function showComparison() {
  console.log(`
📊 对比：旧方式 vs 新方式
========================

🔴 旧方式（依赖包装器）:
import { UserPurchase } from '../wrappers/UserPurchase'

const userPurchase = client.open(UserPurchase.createFromAddress(address))
const count = await userPurchase.getPurchaseIdCounter()
const details = await userPurchase.getPurchaseDetails(1)

❌ 问题：
- 依赖复杂的合约包装器
- 需要合约编译结果
- 难以调试和维护

🟢 新方式（独立调用）:
import { callContractMethod } from './contractUtils'

const countResult = await callContractMethod(address, 'purchase_id_counter')
const count = countResult.stack.readNumber()

const detailsResult = await callContractMethod(address, 'purchase_details', [1])
const details = parsePurchaseRecord(detailsResult)

✅ 优势：
- 依赖简单，只需要 TON SDK
- 不需要合约编译结果
- 更容易理解和调试
- 更好的错误处理
- 更灵活的参数处理
  `)
}

// 如果直接运行此文件，执行演示
if (typeof window === 'undefined' && require.main === module) {
  showComparison()
  demonstrateIndependentCalls()
}

export {
  callContractMethod,
  parsePurchaseRecord,
  getUserPurchaseData,
  demonstrateIndependentCalls
}
