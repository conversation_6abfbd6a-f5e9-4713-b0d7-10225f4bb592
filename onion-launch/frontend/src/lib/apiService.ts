import axios from 'axios';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for API responses
export interface AuctionState {
  current_round: number;
  current_price: string;
  total_raised: string;
  total_tokens_sold: string;
  auction_status: number;
  purchase_count: number;
}

export interface PurchaseCalculation {
  user: string;
  amount: string;
  currency: number;
  tokens_to_receive: string;
  current_price: string;
  current_round: number;
  timestamp: number;
  nonce: string;
  usdt_equivalent_amount: string; // Amount converted to USDT units for cap checking
}

export interface PurchaseCalculationResponse {
  calculation: PurchaseCalculation;
  signature: string;
  success: boolean;
  error?: string;
}

export interface PurchaseRequest {
  user_address: string;
  amount: string;
  currency: number;
}

export interface PublicKeyResponse {
  public_key_hex: string;
  public_key_bigint: string;
  note: string;
}

// API Service Class
export class ApiService {
  /**
   * Get current auction state
   */
  static async getAuctionState(): Promise<AuctionState> {
    try {
      const response = await apiClient.get<AuctionState>('/auction/state');
      return response.data;
    } catch (error) {
      console.error('Failed to get auction state:', error);
      throw new Error('Failed to fetch auction state');
    }
  }

  /**
   * Calculate purchase and get signature
   * This is the only supported purchase method - all purchases require signature verification
   */
  static async calculatePurchase(request: PurchaseRequest): Promise<PurchaseCalculationResponse> {
    try {
      const response = await apiClient.post<PurchaseCalculationResponse>('/purchase/calculate', request);
      return response.data;
    } catch (error) {
      console.error('Failed to calculate purchase:', error);
      if (axios.isAxiosError(error) && error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to calculate purchase');
    }
  }

  /**
   * Get server public key
   */
  static async getPublicKey(): Promise<PublicKeyResponse> {
    try {
      const response = await apiClient.get<PublicKeyResponse>('/config/public-key');
      return response.data;
    } catch (error) {
      console.error('Failed to get public key:', error);
      throw new Error('Failed to fetch public key');
    }
  }

  /**
   * Verify signature (for testing)
   */
  static async verifySignature(calculation: PurchaseCalculation, signature: string): Promise<{ valid: boolean; message: string }> {
    try {
      const response = await apiClient.post('/signature/verify', {
        calculation,
        signature
      });
      return response.data;
    } catch (error) {
      console.error('Failed to verify signature:', error);
      throw new Error('Failed to verify signature');
    }
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{ status: string; timestamp: string; service: string }> {
    try {
      const response = await apiClient.get('/health');
      return response.data;
    } catch (error) {
      console.error('Health check failed:', error);
      throw new Error('Service unavailable');
    }
  }
}

// Helper functions for data conversion
export const formatTokenAmount = (amount: string): string => {
  const num = parseFloat(amount);
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toLocaleString();
};

export const formatPrice = (price: string): string => {
  const num = parseFloat(price) / **********; // Convert from nanotons
  return num.toFixed(3);
};

export const formatCurrency = (amount: string, decimals: number = 9): string => {
  const num = parseFloat(amount) / Math.pow(10, decimals);
  return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
};

// Error handling helper
export const handleApiError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unexpected error occurred';
};
