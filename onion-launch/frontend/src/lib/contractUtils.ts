/**
 * Independent contract interaction utilities
 * 
 * This module provides simple, dependency-free methods for calling
 * contract get methods and parsing results, avoiding complex wrapper dependencies.
 */

import { Address, TonClient, TupleBuilder, TupleItem, TupleReader } from '@ton/ton'

// Purchase record structure matching the contract
export interface PurchaseRecord {
  id: number;
  user: Address;
  amount: bigint;
  tokens: bigint;
  timestamp: number;
  currency: number; // 0=TON, 1=USDT
  purchase_method: number; // 0=direct, 1=signature_verified
  nonce: bigint;
}

/**
 * Independent method to call contract get methods
 * Provides a simple interface without wrapper dependencies
 */
export const callContractGetMethod = async (
  client: TonClient,
  address: Address,
  method: string,
  args?: TupleItem[]
) => {
  return await client.runMethod(address, method, args)
}

/**
 * Parse purchase details from contract response
 * Converts raw stack data to structured PurchaseRecord object
 */
export const parsePurchaseDetails = (result: any): PurchaseRecord | null => {
  try {
    if (result.stack.remaining === 0) {
      return null
    }

    // Read the PurchaseRecord struct from the stack
    const source: TupleReader = result.stack.readTupleOpt();
    const _id = source.pop();
    const _user = source.pop();
    const _amount = source.pop();
    const _tokens = source.pop();
    const _timestamp = source.pop();
    const _currency = source.pop();
    const _purchase_method = source.pop();
    const _nonce = source.pop();

    return {
      id: Number(_id),
      amount: _amount,
      tokens: _tokens,
      timestamp: Number(_timestamp),
      currency: Number(_currency),
      purchase_method: Number(_purchase_method),
      nonce: Number(_nonce),
      user: _user,
    }
  } catch (error) {
    console.error('Error parsing purchase details:', error)
    return null
  }
}

/**
 * Get purchase count from UserPurchase contract
 */
export const getPurchaseCount = async (
  client: TonClient,
  userPurchaseAddress: Address
): Promise<number> => {
  const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_id_counter', [])
  return result.stack.readNumber()
}

/**
 * Get purchase details by ID
 */
export const getPurchaseDetails = async (
  client: TonClient,
  userPurchaseAddress: Address,
  purchaseId: number
): Promise<PurchaseRecord | null> => {
  const args = new TupleBuilder();
  args.writeNumber(purchaseId);
  const result = await callContractGetMethod(client, userPurchaseAddress, 'purchase_details', args.build())
  return parsePurchaseDetails(result)
}

/**
 * Check if purchase is refunded
 */
export const isRefunded = async (
  client: TonClient,
  userPurchaseAddress: Address,
  purchaseId: number
): Promise<boolean> => {
  const args = new TupleBuilder();
  args.writeNumber(purchaseId);
  const result = await callContractGetMethod(client, userPurchaseAddress, 'is_refunded', args.build())
  return result.stack.readBoolean()
}

/**
 * Get total purchased tokens
 */
export const getTotalPurchased = async (
  client: TonClient,
  userPurchaseAddress: Address
): Promise<bigint> => {
  const result = await callContractGetMethod(client, userPurchaseAddress, 'total_purchased', [])
  return result.stack.readBigNumber()
}

/**
 * Get total paid amount
 */
export const getTotalPaid = async (
  client: TonClient,
  userPurchaseAddress: Address
): Promise<bigint> => {
  const result = await callContractGetMethod(client, userPurchaseAddress, 'total_paid', [])
  return result.stack.readBigNumber()
}

/**
 * Get signature verified purchases count
 */
export const getSignatureVerifiedPurchases = async (
  client: TonClient,
  userPurchaseAddress: Address
): Promise<number> => {
  const result = await callContractGetMethod(client, userPurchaseAddress, 'signature_verified_purchases', [])
  return result.stack.readNumber()
}
