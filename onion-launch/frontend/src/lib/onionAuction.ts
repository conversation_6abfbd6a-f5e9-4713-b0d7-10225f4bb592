import { Address, beginCell, Cell, Contract, contractAddress, Contract<PERSON><PERSON><PERSON>, Sender, SendMode, Dictionary } from '@ton/core'

export type RoundStats = {
    round_number: number
    start_time: number
    end_time: number
    price: bigint
    total_raised_ton: bigint
    total_raised_usdt: bigint
    tokens_sold: bigint
    purchase_count: number
    unique_users: number
    refund_count: number
    refunded_amount_ton: bigint
    refunded_amount_usdt: bigint
}

// RoundStats 解析器，符合 DictionaryValue 接口
const RoundStatsValue = {
    serialize: (src: RoundStats): Cell => {
        return beginCell()
            .storeUint(src.round_number, 32)
            .storeUint(src.start_time, 64)
            .storeUint(src.end_time, 64)
            .storeCoins(src.price)
            .storeCoins(src.total_raised_ton)
            .storeCoins(src.total_raised_usdt)
            .storeCoins(src.tokens_sold)
            .storeUint(src.purchase_count, 32)
            .storeUint(src.unique_users, 32)
            .storeUint(src.refund_count, 32)
            .storeCoins(src.refunded_amount_ton)
            .storeCoins(src.refunded_amount_usdt)
            .endCell()
    },
    parse: (src: any): RoundStats => {
        try {
            return {
                round_number: src.loadUint(32),
                start_time: src.loadUint(64),
                end_time: src.loadUint(64),
                price: src.loadCoins(),
                total_raised_ton: src.loadCoins(),
                total_raised_usdt: src.loadCoins(),
                tokens_sold: src.loadCoins(),
                purchase_count: src.loadUint(32),
                unique_users: src.loadUint(32),
                refund_count: src.loadUint(32),
                refunded_amount_ton: src.loadCoins(),
                refunded_amount_usdt: src.loadCoins()
            }
        } catch (e) {
            console.error('Failed to parse RoundStats from slice:', e)
            throw e
        }
    }
}



export type OnionAuctionConfig = {
    owner: Address
    startTime: number
    endTime: number
    softCap: bigint
    hardCap: bigint
    totalSupply: bigint
}

export function onionAuctionConfigToCell(config: OnionAuctionConfig): Cell {
    return beginCell()
        .storeAddress(config.owner)
        .storeUint(config.startTime, 64)
        .storeUint(config.endTime, 64)
        .storeCoins(config.softCap)
        .storeCoins(config.hardCap)
        .storeCoins(config.totalSupply)
        .endCell()
}

export class OnionAuction implements Contract {
    constructor(readonly address: Address, readonly init?: { code: Cell; data: Cell }) {}

    static createFromAddress(address: Address) {
        return new OnionAuction(address)
    }

    static createFromConfig(config: OnionAuctionConfig, code: Cell, workchain = 0) {
        const data = onionAuctionConfigToCell(config)
        const init = { code, data }
        return new OnionAuction(contractAddress(workchain, init), init)
    }

    async sendDeploy(provider: ContractProvider, via: Sender, value: bigint) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell().endCell(),
        })
    }

    async sendPurchase(
        provider: ContractProvider,
        via: Sender,
        opts: {
            amount: bigint
            currency: number
            value: bigint
        }
    ) {
        await provider.internal(via, {
            value: opts.value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0x1234, 32) // op code for Purchase
                .storeUint(0, 64) // query_id
                .storeCoins(opts.amount)
                .storeUint(opts.currency, 8)
                .endCell(),
        })
    }

    async sendRefund(
        provider: ContractProvider,
        via: Sender,
        opts: {
            purchaseId: number
            value: bigint
        }
    ) {
        await provider.internal(via, {
            value: opts.value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0x5678, 32) // op code for Refund
                .storeUint(0, 64) // query_id
                .storeUint(opts.purchaseId, 32)
                .endCell(),
        })
    }

    async getAuctionInfo(provider: ContractProvider) {
        const result = await provider.get('auction_info', [])
        return {
            startTime: result.stack.readNumber(),
            endTime: result.stack.readNumber(),
            softCap: result.stack.readBigNumber(),
            hardCap: result.stack.readBigNumber(),
            totalSupply: result.stack.readBigNumber(),
            refundFeePercent: result.stack.readNumber(),
        }
    }

    /**
     * 获取所有轮次的汇总信息
     * 调用合约的 all_rounds_summary getter，使用 Dictionary 优化解析 map<Int, RoundStats>
     * @param provider 合约提供者
     * @returns 包含所有轮次统计数据的数组
     */
    async getAllRounds(provider: ContractProvider): Promise<RoundStats[]> {
        try {
            const result = await provider.get('all_rounds_summary', [])
            
            // 合约返回 map<Int, RoundStats>
            const dictCell = result.stack.readCellOpt()
            
            if (!dictCell) {
                console.log('No dictionary cell returned, falling back to individual queries')
                return await this.getAllRoundsFallback(provider)
            }
            
            // 检查字典是否为空
            if (dictCell.bits.length === 0 && dictCell.refs.length === 0) {
                console.log('Empty dictionary returned')
                return []
            }
            
            // 尝试使用 Dictionary 类解析 map<Int, RoundStats>
            try {
                // 先尝试简单的字典解析，使用 Cell 值而不是自定义解析器
                const roundsDict = Dictionary.load(
                    Dictionary.Keys.Int(32), // 键是 32 位无符号整数（轮次号）
                    Dictionary.Values.Cell(), // 先使用 Cell 值，手动解析
                    dictCell
                )
                
                const rounds: RoundStats[] = []
                console.log(`Dictionary loaded successfully, found ${roundsDict.size} rounds`)
                
                // 遍历字典中的所有条目
                for (const [roundNumber, statsCell] of roundsDict) {
                    try {
                        const statsSlice = statsCell.beginParse()
                        
                        const roundStats: RoundStats = {
                            round_number: statsSlice.loadUint(32),
                            start_time: statsSlice.loadUint(64),
                            end_time: statsSlice.loadUint(64),
                            price: statsSlice.loadCoins(),
                            total_raised_ton: statsSlice.loadCoins(),
                            total_raised_usdt: statsSlice.loadCoins(),
                            tokens_sold: statsSlice.loadCoins(),
                            purchase_count: statsSlice.loadUint(32),
                            unique_users: statsSlice.loadUint(32),
                            refund_count: statsSlice.loadUint(32),
                            refunded_amount_ton: statsSlice.loadCoins(),
                            refunded_amount_usdt: statsSlice.loadCoins()
                        }
                        
                        rounds.push(roundStats)
                    } catch (parseError) {
                        console.warn(`Failed to parse round ${roundNumber}:`, parseError)
                        continue
                    }
                }
                
                // 按轮次号排序
                rounds.sort((a, b) => a.round_number - b.round_number)
                
                console.log(`Successfully parsed ${rounds.length} rounds from dictionary`)
                return rounds
                
            } catch (dictError) {
                console.warn('Dictionary parsing failed:', dictError instanceof Error ? dictError.message : String(dictError))
                
                // 如果字典解析失败，回退到逐个查询的方法
                console.log('Falling back to individual round queries...')
                return await this.getAllRoundsFallback(provider)
            }
        } catch (e) {
            console.warn('Failed to get all rounds summary from contract:', e)
            console.log('Contract may not support all_rounds_summary getter, falling back to individual queries')
            
            // 如果合约不支持 all_rounds_summary getter，回退到逐个查询
            try {
                return await this.getAllRoundsFallback(provider)
            } catch (fallbackError) {
                console.error('Fallback method also failed:', fallbackError)
                return []
            }
        }
    }

    /**
     * 回退方法：逐个查询轮次数据
     * 当字典解析失败时使用此方法
     */
    private async getAllRoundsFallback(provider: ContractProvider): Promise<RoundStats[]> {
        try {
            // 获取总轮次数
            const totalRoundsResult = await provider.get('total_rounds', [])
            const totalRounds = totalRoundsResult.stack.readNumber()
            
            if (totalRounds === 0) {
                return []
            }
            
            const rounds: RoundStats[] = []
            for (let i = 1; i <= totalRounds; i++) {
                try {
                    const roundResult = await provider.get('round_summary', [
                        { type: 'int', value: BigInt(i) }
                    ])
                    
                    const roundStats: RoundStats = {
                        round_number: roundResult.stack.readNumber(),
                        start_time: roundResult.stack.readNumber(),
                        end_time: roundResult.stack.readNumber(),
                        price: roundResult.stack.readBigNumber(),
                        total_raised_ton: roundResult.stack.readBigNumber(),
                        total_raised_usdt: roundResult.stack.readBigNumber(),
                        tokens_sold: roundResult.stack.readBigNumber(),
                        purchase_count: roundResult.stack.readNumber(),
                        unique_users: roundResult.stack.readNumber(),
                        refund_count: roundResult.stack.readNumber(),
                        refunded_amount_ton: roundResult.stack.readBigNumber(),
                        refunded_amount_usdt: roundResult.stack.readBigNumber()
                    }
                    
                    rounds.push(roundStats)
                } catch (e) {
                    console.warn(`Failed to get round ${i} stats:`, e)
                    continue
                }
            }
            
            return rounds
        } catch (e) {
            console.warn('Fallback method also failed:', e)
            return []
        }
    }

    /**
     * 获取聚合统计数据
     * 调用合约的 aggregated_stats getter，返回所有轮次的累计统计信息
     * @param provider 合约提供者
     * @returns 包含所有轮次聚合数据的统计对象数组
     */
    async getAggregatedStats(provider: ContractProvider): Promise<RoundStats[]> {
        const result = await provider.get('aggregated_stats', [])
        const stats = {
            round_number: result.stack.readNumber(), // 0 表示聚合统计
            start_time: result.stack.readNumber(),
            end_time: result.stack.readNumber(),
            price: result.stack.readBigNumber(), // 对于聚合统计不适用，返回0
            total_raised_ton: result.stack.readBigNumber(),
            total_raised_usdt: result.stack.readBigNumber(),
            tokens_sold: result.stack.readBigNumber(),
            purchase_count: result.stack.readNumber(),
            unique_users: result.stack.readNumber(), // 注意：可能会重复计算跨轮次的用户
            refund_count: result.stack.readNumber(),
            refunded_amount_ton: result.stack.readBigNumber(),
            refunded_amount_usdt: result.stack.readBigNumber()
        }
        
        // 将统计数据包装在数组中返回
        return [stats]
    }

    async getCurrentRound(provider: ContractProvider) {
        const result = await provider.get('current_round', [])
        return result.stack.readNumber()
    }

    async getCurrentPrice(provider: ContractProvider) {
        const result = await provider.get('current_price', [])
        return result.stack.readBigNumber()
    }

    async getTotalRaised(provider: ContractProvider) {
        const result = await provider.get('total_raised', [])
        return result.stack.readBigNumber()
    }

    async getTotalTokensSold(provider: ContractProvider) {
        const result = await provider.get('total_tokens_sold', [])
        return result.stack.readBigNumber()
    }

    async getAuctionStatus(provider: ContractProvider) {
        const result = await provider.get('auction_status', [])
        return result.stack.readNumber()
    }

    async getRemainingTokens(provider: ContractProvider) {
        const result = await provider.get('remaining_tokens', [])
        return result.stack.readBigNumber()
    }

    async getUserPurchaseAddress(provider: ContractProvider, user: Address) {
        const result = await provider.get('user_purchase_address', [
            { type: 'slice', cell: beginCell().storeAddress(user).endCell() }
        ])
        return result.stack.readAddressOpt()
    }

    async isAuctionActive(provider: ContractProvider) {
        const result = await provider.get('is_auction_active', [])
        return result.stack.readBoolean()
    }

    // Treasury management methods
    async sendSetTreasury(provider: ContractProvider, via: Sender, value: bigint, treasuryAddress: Address) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0xd3d3d3d3, 32) // SetTreasury op code
                .storeAddress(treasuryAddress)
                .endCell(),
        })
    }

    // Fund withdrawal methods
    async sendWithdrawTON(provider: ContractProvider, via: Sender, value: bigint, amount: bigint, destination: Address) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0xd1d1d1d1, 32) // WithdrawTON op code
                .storeCoins(amount) // 0 = withdraw all
                .storeAddress(destination)
                .endCell(),
        })
    }

    async sendWithdrawUSDT(provider: ContractProvider, via: Sender, value: bigint, amount: bigint, destination: Address) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell()
                .storeUint(0xd2d2d2d2, 32) // WithdrawUSDT op code
                .storeCoins(amount) // 0 = withdraw all
                .storeAddress(destination)
                .endCell(),
        })
    }

    // Withdrawal query methods
    async getTreasuryAddress(provider: ContractProvider) {
        const result = await provider.get('treasury_address', [])
        return result.stack.readAddressOpt()
    }

    async getWithdrawableTON(provider: ContractProvider) {
        const result = await provider.get('withdrawable_ton', [])
        return result.stack.readBigNumber()
    }

    async getWithdrawableUSDT(provider: ContractProvider) {
        const result = await provider.get('withdrawable_usdt', [])
        return result.stack.readBigNumber()
    }

    async canWithdraw(provider: ContractProvider) {
        const result = await provider.get('can_withdraw', [])
        return result.stack.readBoolean()
    }

    async getWithdrawalSummary(provider: ContractProvider) {
        const result = await provider.get('withdrawal_summary', [])
        const summary = new Map<number, bigint>()

        // Read the map from the stack
        // Note: This is a simplified version - actual implementation may vary
        try {
            while (result.stack.remaining > 0) {
                const key = result.stack.readNumber()
                const value = result.stack.readBigNumber()
                summary.set(key, value)
            }
        } catch (e) {
            // Handle end of stack
        }

        return {
            auctionStatus: summary.get(1) || 0n,
            withdrawableTON: summary.get(2) || 0n,
            withdrawableUSDT: summary.get(3) || 0n
        }
    }

    // USDT related getters
    async getTotalRaisedUSDT(provider: ContractProvider) {
        const result = await provider.get('total_raised_usdt', [])
        return result.stack.readBigNumber()
    }

    async getTotalRaisedEquivalent(provider: ContractProvider) {
        const result = await provider.get('total_raised_equivalent', [])
        return result.stack.readBigNumber()
    }

    async isUSDTEnabled(provider: ContractProvider) {
        const result = await provider.get('is_usdt_enabled', [])
        return result.stack.readBoolean()
    }
}