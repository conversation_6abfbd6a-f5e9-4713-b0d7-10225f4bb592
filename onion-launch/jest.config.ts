import type { Config } from 'jest';

const config: Config = {
    preset: 'ts-jest',
    // globalSetup: './jest.setup.ts', // Temporarily disabled
    cache: false,
    testEnvironment: 'node', // Changed from @ton/sandbox/jest-environment
    testPathIgnorePatterns: ['/node_modules/', '/dist/'],
    reporters: ['default'],
    // setupFilesAfterEnv: ['<rootDir>/jest.bigint-setup.ts'], // Temporarily disabled
    transform: {
        '^.+\\.ts$': ['ts-jest', {
            tsconfig: {
                target: 'ES2020',
                module: 'commonjs',
                esModuleInterop: true,
                allowSyntheticDefaultImports: true,
                skipLibCheck: true
            }
        }]
    },
    moduleFileExtensions: ['ts', 'js'],
    testMatch: ['**/*.spec.ts'],
    verbose: false
};

export default config;
