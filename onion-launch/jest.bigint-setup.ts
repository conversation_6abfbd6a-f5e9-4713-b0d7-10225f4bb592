// Setup for handling BigInt serialization in Jest
// This prevents "Do not know how to serialize a BigInt" errors

// Override JSON.stringify to handle BigInt safely
try {
    if (typeof BigInt !== 'undefined' && !(BigInt.prototype as any).toJSON) {
        Object.defineProperty(BigInt.prototype, 'toJSON', {
            value: function() {
                return this.toString();
            },
            configurable: true,
            writable: true,
            enumerable: false
        });
    }
} catch (error) {
    // Silently ignore if we can't set the property
    console.warn('Could not set BigInt.prototype.toJSON:', error);
}

// Add custom matcher for BigInt comparison
expect.extend({
    toBeBigInt(received: any, expected: bigint) {
        const pass = typeof received === 'bigint' && received === expected;
        if (pass) {
            return {
                message: () => `expected ${received} not to be ${expected}`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected ${received} to be ${expected}`,
                pass: false,
            };
        }
    },
});

