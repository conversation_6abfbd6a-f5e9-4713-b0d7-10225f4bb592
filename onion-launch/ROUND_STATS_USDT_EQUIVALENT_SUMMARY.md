# RoundStats USDT 等价金额统计功能实现总结

## 🎯 目标

在每一轮的信息统计 RoundStats 中加入 `raised_usdt_equivalent` 和 `refunded_usdt_equivalent` 的统计，以便更好地跟踪每轮的 USDT 等价金额。

## ✅ 完成的修改

### 1. RoundStats 结构更新

在 `RoundStats` 结构中添加了两个新字段：

```tact
struct RoundStats {
    round_number: Int as uint32;
    start_time: Int as uint64;
    end_time: Int as uint64;
    price: Int as coins;
    total_raised_ton: Int as coins;
    total_raised_usdt: Int as coins;
    raised_usdt_equivalent: Int as coins; // 新增：本轮总的 USDT 等价金额
    tokens_sold: Int as coins;
    purchase_count: Int as uint32;
    unique_users: Int as uint32;
    refund_count: Int as uint32;
    refunded_amount_ton: Int as coins;
    refunded_amount_usdt: Int as coins;
    refunded_usdt_equivalent: Int as coins; // 新增：本轮退款的 USDT 等价金额
}
```

### 2. 初始化函数更新

更新了所有创建 RoundStats 的函数，确保新字段被正确初始化：

#### `initializeFirstRound()`
- 初始化第一轮时，将 `raised_usdt_equivalent` 和 `refunded_usdt_equivalent` 设为 0

#### `initializeNewRound(round_number, start_time)`
- 创建新轮次时，将新字段初始化为 0

#### `finalizeCurrentRound(current_time)`
- 结束当前轮次时，保持现有的统计数据

### 3. 统计更新函数修改

#### `updateRoundStatsForPurchase()` 函数签名更新
```tact
// 之前
fun updateRoundStatsForPurchase(user: Address, amount: Int, tokens: Int, currency: Int)

// 现在
fun updateRoundStatsForPurchase(user: Address, amount: Int, tokens: Int, currency: Int, usdt_equivalent_amount: Int)
```

**功能增强：**
- 在每次购买时，累加 `raised_usdt_equivalent += usdt_equivalent_amount`
- 确保每轮的 USDT 等价金额统计准确

#### `updateRoundStatsForRefund()` 函数签名更新
```tact
// 之前
fun updateRoundStatsForRefund(round_number: Int, amount: Int, currency: Int)

// 现在
fun updateRoundStatsForRefund(round_number: Int, amount: Int, currency: Int, usdt_equivalent_amount: Int)
```

**功能增强：**
- 在每次退款时，累加 `refunded_usdt_equivalent += usdt_equivalent_amount`
- 准确跟踪每轮的退款 USDT 等价金额

### 4. 调用点更新

更新了所有调用统计函数的地方，传递正确的 `usdt_equivalent_amount` 参数：

#### PurchaseWithSignature 接收器
```tact
self.updateRoundStatsForPurchase(calc.user, calc.amount, calc.tokens_to_receive, calc.currency, calc.usdt_equivalent_amount);
```

#### handleUSDTWithSignature 函数
```tact
self.updateRoundStatsForPurchase(calc.user, calc.amount, calc.tokens_to_receive, 1, calc.usdt_equivalent_amount);
```

#### ProcessRefund 接收器
```tact
self.updateRoundStatsForRefund(msg.round_number, msg.amount, msg.currency, msg.usdt_equivalent_amount);
```

### 5. Getter 函数更新

#### `aggregated_stats()` 函数增强
添加了 USDT 等价金额的聚合统计：

```tact
get fun aggregated_stats(): RoundStats {
    let total_raised_usdt_equivalent: Int = 0;
    let total_refunded_usdt_equivalent: Int = 0;
    
    // 遍历所有轮次，累加统计
    while (i <= max_rounds) {
        // ...
        total_raised_usdt_equivalent += stats.raised_usdt_equivalent;
        total_refunded_usdt_equivalent += stats.refunded_usdt_equivalent;
        // ...
    }
    
    return RoundStats{
        // ...
        raised_usdt_equivalent: total_raised_usdt_equivalent,
        refunded_usdt_equivalent: total_refunded_usdt_equivalent
        // ...
    };
}
```

#### `round_summary()` 函数更新
确保返回的活跃轮次统计包含新字段：

```tact
return RoundStats{
    // ...
    raised_usdt_equivalent: current_stats.raised_usdt_equivalent,
    refunded_usdt_equivalent: current_stats.refunded_usdt_equivalent
    // ...
};
```

### 6. StartAuction 接收器更新

更新第一轮统计时，确保包含新字段：

```tact
let updated_stats: RoundStats = RoundStats{
    // ...
    raised_usdt_equivalent: stats.raised_usdt_equivalent,
    refunded_usdt_equivalent: stats.refunded_usdt_equivalent
    // ...
};
```

## 🔧 技术实现细节

### 统计逻辑

1. **购买统计**：每次购买时，将 `calc.usdt_equivalent_amount` 累加到当前轮次的 `raised_usdt_equivalent`
2. **退款统计**：每次退款时，将 `msg.usdt_equivalent_amount` 累加到对应轮次的 `refunded_usdt_equivalent`
3. **聚合统计**：在 `aggregated_stats()` 中汇总所有轮次的 USDT 等价金额

### 数据一致性

- 轮次级别的 `raised_usdt_equivalent` 统计与合约级别的 `total_raised_usdt_equivalent` 保持一致
- 退款时同时更新轮次统计和合约总统计
- 所有 RoundStats 构造都包含完整的字段，确保数据完整性

## 📊 使用场景

1. **轮次分析**：可以查看每轮的 USDT 等价收入和退款情况
2. **趋势分析**：通过 `aggregated_stats()` 获取整体的 USDT 等价金额趋势
3. **财务报告**：准确的 USDT 等价金额统计便于财务分析
4. **Cap 监控**：结合轮次统计监控接近 hard cap 的情况

## 🚀 优势

1. **统一单位**：所有统计都基于 USDT 等价单位，便于比较和分析
2. **精确跟踪**：每轮的收入和退款都有准确的 USDT 等价金额记录
3. **完整性**：购买和退款的 USDT 等价金额都被完整跟踪
4. **可扩展性**：为未来的财务分析和报告功能奠定基础

## 📝 注意事项

1. **向后兼容性**：这些更改会影响现有的 RoundStats 结构，需要重新部署合约
2. **测试验证**：建议充分测试所有轮次统计功能，确保数据准确性
3. **前端适配**：前端代码需要适配新的 RoundStats 字段结构
