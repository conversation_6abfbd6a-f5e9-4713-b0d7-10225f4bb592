# 轮次跟踪功能实现总结

## 🎯 功能概述

为 OnionAuction 合约添加了完整的轮次跟踪功能，可以详细监控每一轮的拍卖数据，包括：
- 每轮的筹集金额（TON 和 USDT）
- 每轮的参与用户数量
- 每轮的购买次数和代币销售量
- 每轮的退款统计
- 用户跨轮次的参与记录

## 🏗️ 核心数据结构

### RoundStats 结构体
```tact
struct RoundStats {
    round_number: Int as uint32;        // 轮次编号
    start_time: Int as uint64;          // 开始时间
    end_time: Int as uint64;            // 结束时间
    price: Int as coins;                // 该轮价格
    total_raised_ton: Int as coins;     // TON 筹集总额
    total_raised_usdt: Int as coins;    // USDT 筹集总额
    tokens_sold: Int as coins;          // 代币销售数量
    purchase_count: Int as uint32;      // 购买次数
    unique_users: Int as uint32;        // 唯一用户数
    refund_count: Int as uint32;        // 退款次数
    refunded_amount_ton: Int as coins;  // TON 退款总额
    refunded_amount_usdt: Int as coins; // USDT 退款总额
}
```

## 📊 合约状态变量

### OnionAuction 合约新增状态
```tact
// 轮次跟踪
round_stats: map<Int, RoundStats>;              // 轮次编号 -> 轮次统计
round_users: map<Int, map<Address, Bool>>;      // 轮次编号 -> 用户地址 -> 是否参与
user_rounds: map<Address, map<Int, Bool>>;      // 用户地址 -> 轮次编号 -> 是否参与
```

### UserPurchase 合约更新
```tact
// 购买记录增加轮次信息
struct PurchaseRecord {
    // ... 原有字段
    round_number: Int as uint32;        // 购买时的轮次
}

// 消息增加轮次信息
message CreateUserPurchase {
    // ... 原有字段
    round_number: Int as uint32;        // 购买时的轮次
}

message ProcessRefund {
    // ... 原有字段
    round_number: Int as uint32;        // 退款对应的轮次
}
```

## 🔧 核心功能实现

### 1. 轮次初始化
```tact
// 初始化第一轮
fun initializeFirstRound() {
    let first_round: RoundStats = RoundStats{
        round_number: 1,
        start_time: 0,  // 拍卖开始时设置
        end_time: 0,    // 拍卖开始时设置
        price: self.current_price,
        // ... 其他字段初始化为0
    };
    self.round_stats.set(1, first_round);
}
```

### 2. 轮次更新逻辑
```tact
// 更新当前轮次
fun updateCurrentRound(current_time: Int) {
    let elapsed_time: Int = current_time - self.auction_config.start_time;
    let new_round: Int = (elapsed_time / self.ROUND_DURATION) + 1;
    
    if (new_round > self.current_round) {
        // 结束当前轮次
        self.finalizeCurrentRound(current_time);
        
        // 开始新轮次
        self.current_round = new_round;
        self.current_price += self.PRICE_INCREMENT * (new_round - self.current_round);
        
        // 初始化新轮次统计
        self.initializeNewRound(new_round, current_time);
    }
}
```

### 3. 购买统计更新
```tact
// 更新轮次购买统计
fun updateRoundStatsForPurchase(user: Address, amount: Int, tokens: Int, currency: Int) {
    let current_stats: RoundStats? = self.round_stats.get(self.current_round);
    if (current_stats != null) {
        let stats: RoundStats = current_stats!!;
        
        // 检查是否为新用户
        let is_new_user: Bool = self.checkAndAddUserToRound(user);
        
        // 更新统计
        let updated_stats: RoundStats = RoundStats{
            // ... 复制原有字段
            total_raised_ton: stats.total_raised_ton + (currency == 0 ? amount : 0),
            total_raised_usdt: stats.total_raised_usdt + (currency == 1 ? amount : 0),
            tokens_sold: stats.tokens_sold + tokens,
            purchase_count: stats.purchase_count + 1,
            unique_users: stats.unique_users + (is_new_user ? 1 : 0),
            // ... 其他字段保持不变
        };
        self.round_stats.set(self.current_round, updated_stats);
    }
}
```

### 4. 退款统计更新
```tact
// 更新轮次退款统计
fun updateRoundStatsForRefund(round_number: Int, amount: Int, currency: Int) {
    let current_stats: RoundStats? = self.round_stats.get(round_number);
    if (current_stats != null) {
        let stats: RoundStats = current_stats!!;
        let updated_stats: RoundStats = RoundStats{
            // ... 复制原有字段
            refund_count: stats.refund_count + 1,
            refunded_amount_ton: stats.refunded_amount_ton + (currency == 0 ? amount : 0),
            refunded_amount_usdt: stats.refunded_amount_usdt + (currency == 1 ? amount : 0)
        };
        self.round_stats.set(round_number, updated_stats);
    }
}
```

## 📋 查询方法 (Getters)

### OnionAuction 合约查询方法
```tact
// 基础查询
get fun round_stats(round_number: Int): RoundStats?
get fun current_round_stats(): RoundStats?
get fun total_rounds(): Int

// 用户相关查询
get fun user_participated_rounds(user: Address): map<Int, Bool>?
get fun round_participants(round_number: Int): map<Address, Bool>?
get fun user_round_count(user: Address): Int

// 汇总查询
get fun round_summary(round_number: Int): RoundStats?
get fun all_rounds_summary(): map<Int, RoundStats>
get fun aggregated_stats(): RoundStats
```

### UserPurchase 合约查询方法
```tact
// 按轮次查询
get fun purchases_by_round(round_number: Int): map<Int, PurchaseRecord>
get fun round_total_amount(round_number: Int): Int
get fun round_total_tokens(round_number: Int): Int
get fun round_purchase_count(round_number: Int): Int

// 用户轮次统计
get fun participated_rounds(): map<Int, Bool>
get fun round_count(): Int
```

## 🔄 集成点

### 1. 购买流程集成
- `receive(msg: Purchase)` - 直接TON购买
- `receive(msg: PurchaseWithSignature)` - 签名验证购买
- `handleUSDTWithSignature()` - USDT签名购买
- `handleDirectUSDTPurchase()` - 直接USDT购买

### 2. 退款流程集成
- `receive(msg: ProcessRefund)` - 处理退款请求
- UserPurchase 合约的 `receive(msg: Refund)` - 发起退款

### 3. 拍卖管理集成
- `receive(msg: StartAuction)` - 初始化第一轮时间
- `updateCurrentRound()` - 自动轮次切换

## 🎯 使用场景

### 1. 实时监控
- 监控当前轮次的实时数据
- 跟踪每轮的参与情况
- 分析轮次间的趋势变化

### 2. 数据分析
- 计算每轮的平均筹集金额
- 分析用户参与模式
- 评估价格递增对参与度的影响

### 3. 用户界面
- 显示历史轮次数据
- 展示用户的参与历史
- 提供详细的拍卖统计

### 4. 审计和报告
- 生成详细的拍卖报告
- 验证资金流向
- 分析退款模式

## ⚡ 性能考虑

### 1. Gas 优化
- 查询操作不消耗 gas
- 状态更新操作已优化
- 批量操作减少交易次数

### 2. 存储优化
- 使用 map 结构高效存储
- 避免重复数据存储
- 按需加载数据

### 3. 计算优化
- 实时计算当前轮次
- 缓存聚合统计
- 延迟计算复杂统计

## 🧪 测试覆盖

### 1. 单元测试
- 轮次初始化测试
- 购买统计更新测试
- 退款统计更新测试
- 用户跟踪测试

### 2. 集成测试
- 多轮次购买流程测试
- 跨轮次用户行为测试
- 聚合统计准确性测试

### 3. 边界测试
- 轮次切换边界测试
- 大量用户参与测试
- 异常情况处理测试

## 📈 扩展性

### 1. 未来增强
- 添加更多统计维度
- 支持自定义时间窗口
- 增加预测分析功能

### 2. 兼容性
- 向后兼容现有功能
- 支持渐进式升级
- 保持API稳定性

这个轮次跟踪系统为 OnionAuction 提供了全面的数据洞察能力，支持实时监控、历史分析和用户行为跟踪，为拍卖管理和决策提供了强有力的数据支持。
