# 资金提取功能使用指南

## 🎯 功能概述

OnionAuction 合约现在支持完整的资金提取功能，允许合约所有者在拍卖成功结束后将筹集到的 TON 和 USDT 提取到指定的钱包地址。

## 🔐 安全机制

### 1. 权限控制
- **仅限所有者**：只有合约所有者可以执行提取操作
- **状态检查**：只有在拍卖成功结束后才能提取资金
- **余额验证**：确保提取金额不超过可用余额

### 2. 提取条件
- 拍卖状态必须为 `ended_success` (状态码 2)
- 筹集金额必须达到软上限 (soft_cap)
- 拍卖时间已结束或达到硬上限 (hard_cap)

## 📋 核心功能

### 1. 设置财库地址
```typescript
// 设置默认的财库地址用于资金接收
await onionAuction.sendSetTreasury(
    provider,
    deployer.getSender(),
    toNano('0.05'), // Gas费用
    treasuryAddress // 财库钱包地址
);
```

### 2. 提取 TON 资金
```typescript
// 提取指定数量的 TON
await onionAuction.sendWithdrawTON(
    provider,
    deployer.getSender(),
    toNano('0.1'), // Gas费用
    toNano('100'), // 提取金额 (0 = 提取全部)
    destinationAddress // 目标钱包地址
);
```

### 3. 提取 USDT 资金
```typescript
// 提取指定数量的 USDT
await onionAuction.sendWithdrawUSDT(
    provider,
    deployer.getSender(),
    toNano('0.1'), // Gas费用
    1000000n, // 提取金额 (USDT 6位小数, 0 = 提取全部)
    destinationAddress // 目标钱包地址
);
```

## 🔍 查询方法

### 1. 检查提取状态
```typescript
// 检查是否可以提取资金
const canWithdraw = await onionAuction.canWithdraw();
console.log(`可以提取资金: ${canWithdraw}`);

// 获取拍卖状态
const auctionStatus = await onionAuction.getAuctionStatus();
console.log(`拍卖状态: ${auctionStatus}`); // 2 = ended_success
```

### 2. 查询可提取金额
```typescript
// 查询可提取的 TON 数量
const withdrawableTON = await onionAuction.getWithdrawableTON();
console.log(`可提取 TON: ${withdrawableTON}`);

// 查询可提取的 USDT 数量
const withdrawableUSDT = await onionAuction.getWithdrawableUSDT();
console.log(`可提取 USDT: ${withdrawableUSDT}`);
```

### 3. 获取财库地址
```typescript
// 查询当前设置的财库地址
const treasuryAddress = await onionAuction.getTreasuryAddress();
if (treasuryAddress) {
    console.log(`财库地址: ${treasuryAddress.toString()}`);
} else {
    console.log('未设置财库地址');
}
```

### 4. 获取提取摘要
```typescript
// 获取完整的提取状态摘要
const summary = await onionAuction.getWithdrawalSummary();
console.log(`拍卖状态: ${summary.auctionStatus}`);
console.log(`可提取 TON: ${summary.withdrawableTON}`);
console.log(`可提取 USDT: ${summary.withdrawableUSDT}`);
```

## 💡 使用示例

### 完整的资金提取流程
```typescript
import { Address, toNano } from '@ton/core';
import { OnionAuction } from './lib/onionAuction';

async function withdrawFunds() {
    // 1. 检查拍卖是否成功结束
    const canWithdraw = await onionAuction.canWithdraw();
    if (!canWithdraw) {
        console.log('拍卖尚未成功结束，无法提取资金');
        return;
    }

    // 2. 查询可提取金额
    const withdrawableTON = await onionAuction.getWithdrawableTON();
    const withdrawableUSDT = await onionAuction.getWithdrawableUSDT();
    
    console.log(`可提取 TON: ${withdrawableTON}`);
    console.log(`可提取 USDT: ${withdrawableUSDT}`);

    // 3. 设置财库地址（如果尚未设置）
    const treasuryAddress = Address.parse('EQC...');
    await onionAuction.sendSetTreasury(
        provider,
        owner.getSender(),
        toNano('0.05'),
        treasuryAddress
    );

    // 4. 提取所有 TON 资金
    if (withdrawableTON > 0n) {
        await onionAuction.sendWithdrawTON(
            provider,
            owner.getSender(),
            toNano('0.1'),
            0n, // 0 表示提取全部
            treasuryAddress
        );
        console.log('TON 资金提取完成');
    }

    // 5. 提取所有 USDT 资金
    if (withdrawableUSDT > 0n) {
        await onionAuction.sendWithdrawUSDT(
            provider,
            owner.getSender(),
            toNano('0.1'),
            0n, // 0 表示提取全部
            treasuryAddress
        );
        console.log('USDT 资金提取完成');
    }
}
```

### 分批提取资金
```typescript
async function withdrawInBatches() {
    const withdrawableTON = await onionAuction.getWithdrawableTON();
    const batchSize = toNano('1000'); // 每次提取 1000 TON
    
    let remaining = withdrawableTON;
    let batchNumber = 1;
    
    while (remaining > 0n) {
        const amountToWithdraw = remaining > batchSize ? batchSize : remaining;
        
        await onionAuction.sendWithdrawTON(
            provider,
            owner.getSender(),
            toNano('0.1'),
            amountToWithdraw,
            treasuryAddress
        );
        
        remaining -= amountToWithdraw;
        console.log(`批次 ${batchNumber} 提取完成: ${amountToWithdraw} TON`);
        batchNumber++;
        
        // 等待交易确认
        await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    console.log('所有资金提取完成');
}
```

## ⚠️ 注意事项

### 1. Gas 费用
- 设置财库地址：约 0.05 TON
- 提取 TON：约 0.1 TON
- 提取 USDT：约 0.1 TON（包含 Jetton 转账费用）

### 2. 提取限制
- 只能在拍卖成功结束后提取
- 只有合约所有者可以执行提取操作
- 不能提取超过可用余额的金额

### 3. USDT 提取
- USDT 提取通过 Jetton 转账实现
- 需要确保已正确配置 USDT 合约地址
- USDT 使用 6 位小数精度

### 4. 安全建议
- 在提取前务必验证目标地址
- 建议先进行小额测试提取
- 保留足够的 TON 用于支付 Gas 费用
- 定期检查合约余额和提取记录

## 🔧 错误处理

### 常见错误码
- `ERROR_WITHDRAWAL_NOT_ALLOWED` (551019): 拍卖尚未成功结束
- `ERROR_INSUFFICIENT_BALANCE` (551020): 余额不足
- `ERROR_INVALID_WITHDRAWAL_ADDRESS` (551021): 无效的提取地址

### 错误处理示例
```typescript
try {
    await onionAuction.sendWithdrawTON(
        provider,
        owner.getSender(),
        toNano('0.1'),
        toNano('100'),
        treasuryAddress
    );
} catch (error) {
    if (error.message.includes('551019')) {
        console.log('错误: 拍卖尚未成功结束');
    } else if (error.message.includes('551020')) {
        console.log('错误: 余额不足');
    } else {
        console.log('提取失败:', error.message);
    }
}
```

## 📊 监控和审计

### 提取记录跟踪
```typescript
// 监控提取交易
async function monitorWithdrawals() {
    const initialTON = await onionAuction.getWithdrawableTON();
    const initialUSDT = await onionAuction.getWithdrawableUSDT();
    
    console.log(`初始可提取 TON: ${initialTON}`);
    console.log(`初始可提取 USDT: ${initialUSDT}`);
    
    // 执行提取操作...
    
    const finalTON = await onionAuction.getWithdrawableTON();
    const finalUSDT = await onionAuction.getWithdrawableUSDT();
    
    console.log(`已提取 TON: ${initialTON - finalTON}`);
    console.log(`已提取 USDT: ${initialUSDT - finalUSDT}`);
}
```

这个资金提取系统为 OnionAuction 提供了安全、灵活的资金管理能力，确保拍卖成功后能够顺利提取筹集到的资金。
