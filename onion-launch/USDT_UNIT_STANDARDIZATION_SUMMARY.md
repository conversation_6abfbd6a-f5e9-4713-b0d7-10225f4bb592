# USDT 单位统一化实现总结

## 🎯 目标

将合约中的 soft cap 和 hard cap 统一使用 USDT 单位表示，并在 PurchaseCalculation 中添加 `usdt_equivalent_amount` 参数来表示本次购买换算为 USDT 的金额。

## ✅ 完成的修改

### 1. 合约层面修改

#### OnionAuction 合约 (`onion-launch/contracts/onion_auction.tact`)

**新增字段：**
- 在 `PurchaseCalculation` 结构中添加了 `usdt_equivalent_amount: Int as coins` 字段
- 在合约状态中添加了 `total_raised_usdt_equivalent: Int as coins` 字段用于统一的 cap 检查

**更新的函数：**
- `hashPurchaseCalculation()`: 添加了 `usdt_equivalent_amount` 到哈希计算中
- `parsePurchaseFromPayload()`: 添加了 `usdt_equivalent_amount` 字段的解析
- `createOrUpdateUserPurchase()`: 更新函数签名，传递 `usdt_equivalent_amount` 参数
- `PurchaseWithSignature` 接收器: 使用 `usdt_equivalent_amount` 进行统一的 cap 检查
- `handleUSDTWithSignature()`: 使用 `usdt_equivalent_amount` 进行统一的 cap 检查
- `ProcessRefund` 接收器: 在退款时正确更新 `total_raised_usdt_equivalent`

**新增 getter：**
- `total_raised_usdt_equivalent()`: 返回 USDT 等价总额用于 cap 检查

#### UserPurchase 合约 (`onion-launch/contracts/user_purchase.tact`)

**更新的结构：**
- `PurchaseRecord`: 添加了 `usdt_equivalent_amount: Int as coins` 字段
- `CreateUserPurchase` 消息: 添加了 `usdt_equivalent_amount: Int as coins` 字段
- `ProcessRefund` 消息: 添加了 `usdt_equivalent_amount: Int as coins` 字段

**修复：**
- 将所有 `throwUnless` 调用替换为 `require` 调用以修复编译错误

### 2. 测试代码更新

更新了以下测试文件中的所有 `PurchaseCalculation` 对象和 helper 函数：

- `OnionAuction.signature.spec.ts`
- `OnionAuction.minPurchase.spec.ts` 
- `integration.signature.spec.ts`
- `UserPurchase.spec.ts`

**主要更改：**
- 在所有 `createPurchaseCalculationCell()` 函数中添加了 `usdt_equivalent_amount` 字段
- 在所有 `calculation` 对象中添加了 `usdt_equivalent_amount` 字段
- 在所有 `CreateUserPurchase` 消息中添加了 `usdt_equivalent_amount` 字段

### 3. Demo 服务器更新

#### 类型定义 (`demo-server/src/types.ts`)
- `PurchaseCalculation` 接口: 添加了 `usdt_equivalent_amount: bigint` 字段
- `PurchaseCalculationSerialized` 接口: 添加了 `usdt_equivalent_amount: string` 字段

#### 计算逻辑 (`demo-server/src/calculator.ts`)
- `generatePurchaseCalculation()`: 添加了 USDT 等价金额的计算逻辑
- 对于 TON 购买：`usdt_equivalent_amount = amount` (假设 1 TON = 1 USDT)
- 对于 USDT 购买：`usdt_equivalent_amount = amount`

#### 签名服务 (`demo-server/src/signer.ts`)
- `createPurchaseCalculationCell()`: 添加了 `usdt_equivalent_amount` 字段到签名数据中

#### API 路由 (`demo-server/src/routes.ts`)
- 更新了响应序列化，添加了 `usdt_equivalent_amount` 字段

### 4. Frontend 更新

#### USDT 购买库 (`frontend/src/lib/usdtPurchase.ts`)
- `PurchaseCalculation` 接口: 添加了 `usdtEquivalentAmount: bigint` 字段
- `buildUSDTForwardPayload()`: 添加了 `usdtEquivalentAmount` 字段到 payload 构建中
- `createPurchaseCalculation()`: 添加了 `usdtEquivalentAmount` 字段的设置

#### API 服务 (`frontend/src/lib/apiService.ts`)
- `PurchaseCalculation` 接口: 添加了 `usdt_equivalent_amount: string` 字段

## 🔧 技术实现细节

### Cap 检查逻辑统一化

**之前：**
```tact
let total_raised_equivalent: Int = self.total_raised + (self.total_raised_usdt * 1000);
if (total_raised_equivalent >= self.auction_config.hard_cap) {
    // 达到硬顶
}
```

**现在：**
```tact
self.total_raised_usdt_equivalent += calc.usdt_equivalent_amount;
if (self.total_raised_usdt_equivalent >= self.auction_config.hard_cap) {
    // 达到硬顶
}
```

### USDT 等价金额计算

**TON 购买：**
- `usdt_equivalent_amount = ton_amount` (假设 1:1 汇率)

**USDT 购买：**
- `usdt_equivalent_amount = usdt_amount`

### 签名验证更新

签名验证现在包含 `usdt_equivalent_amount` 字段，确保数据完整性：

```tact
let data_cell: Cell = beginCell()
    .storeAddress(calc.user)
    .storeCoins(calc.amount)
    .storeUint(calc.currency, 8)
    .storeCoins(calc.tokens_to_receive)
    .storeCoins(calc.current_price)
    .storeUint(calc.current_round, 32)
    .storeUint(calc.timestamp, 64)
    .storeUint(calc.nonce, 64)
    .storeCoins(calc.usdt_equivalent_amount)  // 新增
    .endCell();
```

## 📝 注意事项

1. **类型错误**: 测试文件中可能会出现 TypeScript 类型错误，需要重新编译合约以生成新的类型定义。

2. **汇率假设**: 当前实现假设 1 TON = 1 USDT，在生产环境中应该使用实际汇率。

3. **向后兼容性**: 这些更改会破坏与旧版本的兼容性，需要重新部署合约。

4. **测试验证**: 建议运行完整的测试套件以确保所有功能正常工作。

## 🚀 下一步

1. 编译合约生成新的 TypeScript 类型定义
2. 运行测试套件验证功能
3. 更新部署脚本以使用新的合约版本
4. 在测试网上部署和验证
