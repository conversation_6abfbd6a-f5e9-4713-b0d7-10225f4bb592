import { toNano, Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { NetworkProvider } from '@ton/blueprint';
import { on } from 'events';

export async function run(provider: NetworkProvider) {
    console.log('🚀 Deploying OnionAuction with USDT support...');

    const onionAuction = provider.open(
        OnionAuction.fromAddress(Address.parse('EQAkINCmqtmcb40fvry16VxBfMHG_8socZpoC6Wvu-YGuY-A')) // Replace with actual address
    );

    try {
        // Set USDT configuration
        await onionAuction.send(
            provider.sender(),
            {
                value: toNano('0.02'),
            },
            {
                $$type: 'UpdateRound',
                new_price: toNano('0.1'), // Initial price per token
                round_number: 3n
            }
        );

        console.log('✅ signing key set successfully');
        console.log('⚠️  Remember to update the signing key with the correct one!');

    } catch (error) {
        console.log('❌ Failed to set signing key:', error);
        console.log('💡 You can set it later using the SetSigningKey message');
    }

    return onionAuction.address;
}
