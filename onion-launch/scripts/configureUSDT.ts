import { to<PERSON>ano, Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { NetworkProvider } from '@ton/blueprint';
import { on } from 'events';

export async function run(provider: NetworkProvider) {
    console.log('🚀 Deploying OnionAuction with USDT support...');

    const onionAuction = provider.open(
        OnionAuction.fromAddress(Address.parse('kQCUmdTvMaj1JmC-YhsSfbJdWsi_--8urSYN-DIpXYi_hgvu'))
    );

    // USDT configuration (Mainnet USDT addresses)
    const USDT_MASTER_MAINNET = 'EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs'; // Tether USD on TON
    const USDT_MASTER_TESTNET = 'kQAE8L7GWbNIEtIn2sYs8xaDvKNIBNoiYzUXYqTjg00KTQaL'; // Test USDT

    // Choose the appropriate USDT master address based on network
    const isMainnet = provider.network() === 'mainnet';
    const usdtMasterAddress = isMainnet ? USDT_MASTER_MAINNET : USDT_MASTER_TESTNET;

    console.log(`🔧 Configuring USDT support for ${isMainnet ? 'mainnet' : 'testnet'}...`);
    console.log('USDT Master Address:', usdtMasterAddress);

    // Calculate the USDT wallet address for the auction contract
    // Note: In a real deployment, you would call the USDT master contract's get_wallet_address method
    // For this example, we'll use a placeholder calculation
    
    // This is a simplified approach - in production, you should:
    // 1. Call the USDT master contract's get_wallet_address method
    // 2. Or deploy the USDT wallet first and get its address
    
    console.log('⚠️  Note: You need to calculate the correct USDT wallet address');
    console.log('   Call USDT master contract get_wallet_address method with auction address');
    console.log('   Auction address:', onionAuction.address.toString());

    const placeholderUSDTWallet = 'kQBSEvC1RVsWZSLuxWjZlhSc_oS2NcaSh45JKDRhrrOLffOO'; // null address as placeholder

    try {
        // Set USDT configuration
        await onionAuction.send(
            provider.sender(),
            {
                value: toNano('0.02'),
            },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: Address.parse(usdtMasterAddress),
                usdt_wallet: Address.parse(placeholderUSDTWallet)
            }
        );

        console.log('✅ USDT configuration set successfully');
        console.log('⚠️  Remember to update the USDT wallet address with the correct one!');

    } catch (error) {
        console.log('❌ Failed to set USDT configuration:', error);
        console.log('💡 You can set it later using the SetUSDTAddress message');
    }



    return onionAuction.address;
}
