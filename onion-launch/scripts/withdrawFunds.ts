import { Address, toNano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { NetworkProvider, sleep } from '@ton/blueprint';

export async function run(provider: NetworkProvider, args: string[]) {
    const ui = provider.ui();

    // Get contract address from arguments or user input
    const contractAddress = Address.parse(
        args.length > 0 ? args[0] : await ui.input('OnionAuction contract address')
    );

    // Get treasury address from arguments or user input
    const treasuryAddress = Address.parse(
        args.length > 1 ? args[1] : await ui.input('Treasury wallet address')
    );

    if (!(await provider.isContractDeployed(contractAddress))) {
        ui.write(`❌ Error: Contract at address ${contractAddress} is not deployed!`);
        return;
    }

    const onionAuction = provider.open(OnionAuction.fromAddress(contractAddress));

    ui.write('🔍 Checking auction status...');

    try {
        // Check if withdrawal is allowed
        const canWithdraw = await onionAuction.getCanWithdraw();
        if (!canWithdraw) {
            ui.write('❌ Cannot withdraw funds: Auction has not ended successfully');
            
            const auctionStatus = await onionAuction.getAuctionStatus();
            const statusText = getAuctionStatusText(auctionStatus);
            ui.write(`Current auction status: ${statusText}`);
            return;
        }

        ui.write('✅ Auction ended successfully, withdrawal is allowed');

        // Get withdrawable amounts
        const withdrawableTON = await onionAuction.getWithdrawableTon();
        const withdrawableUSDT = await onionAuction.getWithdrawableUsdt();
        const totalRaised = await onionAuction.getTotalRaised();
        const totalRaisedUSDT = await onionAuction.getTotalRaisedUsdt();

        ui.write('\n📊 Fund Summary:');
        ui.write(`Total TON raised: ${formatTON(totalRaised)}`);
        ui.write(`Total USDT raised: ${formatUSDT(totalRaisedUSDT)}`);
        ui.write(`Withdrawable TON: ${formatTON(withdrawableTON)}`);
        ui.write(`Withdrawable USDT: ${formatUSDT(withdrawableUSDT)}`);

        if (withdrawableTON === 0n && withdrawableUSDT === 0n) {
            ui.write('ℹ️ No funds available for withdrawal');
            return;
        }

        // Check current treasury address
        const currentTreasury = await onionAuction.getTreasuryAddress();
        if (currentTreasury && currentTreasury.toString() !== treasuryAddress.toString()) {
            ui.write(`⚠️ Warning: Current treasury address (${currentTreasury.toString()}) differs from provided address`);
            const shouldUpdate = await ui.choose('Update treasury address?', ['Yes', 'No'], (c) => c);
            
            if (shouldUpdate === 'Yes') {
                ui.write('📝 Setting treasury address...');
                await onionAuction.send(
                    provider.sender(),
                    { value: toNano('0.05') },
                    {
                        $$type: 'SetTreasury',
                        treasury_address: treasuryAddress
                    }
                );
                ui.write('✅ Treasury address updated');
                await sleep(3000); // Wait for transaction confirmation
            }
        } else if (!currentTreasury) {
            ui.write('📝 Setting treasury address...');
            await onionAuction.send(
                provider.sender(),
                { value: toNano('0.05') },
                {
                    $$type: 'SetTreasury',
                    treasury_address: treasuryAddress
                }
            );
            ui.write('✅ Treasury address set');
            await sleep(3000);
        }

        // Withdraw TON if available
        if (withdrawableTON > 0n) {
            const withdrawTON = await ui.choose(
                `Withdraw ${formatTON(withdrawableTON)} TON?`, 
                ['Withdraw All', 'Withdraw Partial', 'Skip'], 
                (c) => c
            );

            if (withdrawTON === 'Withdraw All') {
                ui.write('💰 Withdrawing all TON...');
                await onionAuction.send(
                    provider.sender(),
                    { value: toNano('0.1') },
                    {
                        $$type: 'WithdrawTON',
                        amount: 0n, // 0 means withdraw all
                        destination: treasuryAddress
                    }
                );
                ui.write('✅ TON withdrawal transaction sent');
                await sleep(3000);
            } else if (withdrawTON === 'Withdraw Partial') {
                const amountStr = await ui.input('Enter amount to withdraw (in TON)');
                const amount = toNano(amountStr);
                
                if (amount > withdrawableTON) {
                    ui.write('❌ Error: Amount exceeds withdrawable balance');
                } else {
                    ui.write(`💰 Withdrawing ${formatTON(amount)} TON...`);
                    await onionAuction.send(
                        provider.sender(),
                        { value: toNano('0.1') },
                        {
                            $$type: 'WithdrawTON',
                            amount: amount,
                            destination: treasuryAddress
                        }
                    );
                    ui.write('✅ TON withdrawal transaction sent');
                    await sleep(3000);
                }
            }
        }

        // Withdraw USDT if available
        if (withdrawableUSDT > 0n) {
            const withdrawUSDT = await ui.choose(
                `Withdraw ${formatUSDT(withdrawableUSDT)} USDT?`, 
                ['Withdraw All', 'Withdraw Partial', 'Skip'], 
                (c) => c
            );

            if (withdrawUSDT === 'Withdraw All') {
                ui.write('💰 Withdrawing all USDT...');
                await onionAuction.send(
                    provider.sender(),
                    { value: toNano('0.1') },
                    {
                        $$type: 'WithdrawUSDT',
                        amount: 0n, // 0 means withdraw all
                        destination: treasuryAddress
                    }
                );
                ui.write('✅ USDT withdrawal transaction sent');
                await sleep(3000);
            } else if (withdrawUSDT === 'Withdraw Partial') {
                const amountStr = await ui.input('Enter amount to withdraw (in USDT)');
                const amount = BigInt(parseFloat(amountStr) * 1000000); // Convert to 6 decimal places
                
                if (amount > withdrawableUSDT) {
                    ui.write('❌ Error: Amount exceeds withdrawable balance');
                } else {
                    ui.write(`💰 Withdrawing ${formatUSDT(amount)} USDT...`);
                    await onionAuction.send(
                        provider.sender(),
                        { value: toNano('0.1') },
                        {
                            $$type: 'WithdrawUSDT',
                            amount: amount,
                            destination: treasuryAddress
                        }
                    );
                    ui.write('✅ USDT withdrawal transaction sent');
                    await sleep(3000);
                }
            }
        }

        // Final status check
        ui.write('\n🔍 Checking final status...');
        const finalWithdrawableTON = await onionAuction.getWithdrawableTon();
        const finalWithdrawableUSDT = await onionAuction.getWithdrawableUsdt();

        ui.write('📊 Final Status:');
        ui.write(`Remaining withdrawable TON: ${formatTON(finalWithdrawableTON)}`);
        ui.write(`Remaining withdrawable USDT: ${formatUSDT(finalWithdrawableUSDT)}`);

        if (finalWithdrawableTON === 0n && finalWithdrawableUSDT === 0n) {
            ui.write('🎉 All funds have been successfully withdrawn!');
        }

    } catch (error) {
        ui.write(`❌ Error: ${error}`);
        
        if (error.toString().includes('551019')) {
            ui.write('Reason: Withdrawal not allowed - auction has not ended successfully');
        } else if (error.toString().includes('551020')) {
            ui.write('Reason: Insufficient balance');
        } else if (error.toString().includes('551021')) {
            ui.write('Reason: Invalid withdrawal address');
        }
    }
}

function getAuctionStatusText(status: number): string {
    switch (status) {
        case 0: return 'Pending';
        case 1: return 'Active';
        case 2: return 'Ended Successfully';
        case 3: return 'Ended Failed';
        default: return 'Unknown';
    }
}

function formatTON(amount: bigint): string {
    return `${(Number(amount) / 1e9).toFixed(2)} TON`;
}

function formatUSDT(amount: bigint): string {
    return `${(Number(amount) / 1e6).toFixed(2)} USDT`;
}
