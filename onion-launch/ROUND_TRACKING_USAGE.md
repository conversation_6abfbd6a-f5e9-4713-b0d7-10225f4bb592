# 轮次跟踪功能使用指南

## 概述

OnionAuction 合约现在支持详细的轮次跟踪功能，可以查询每一轮的详细信息，包括：
- 总筹集金额（TON 和 USDT）
- 参与用户数
- 购买次数
- 代币销售数量
- 退款统计

## 数据结构

### RoundStats 结构
```tact
struct RoundStats {
    round_number: Int as uint32;        // 轮次编号
    start_time: Int as uint64;          // 开始时间
    end_time: Int as uint64;            // 结束时间
    price: Int as coins;                // 该轮价格
    total_raised_ton: Int as coins;     // TON 筹集总额
    total_raised_usdt: Int as coins;    // USDT 筹集总额
    tokens_sold: Int as coins;          // 代币销售数量
    purchase_count: Int as uint32;      // 购买次数
    unique_users: Int as uint32;        // 唯一用户数
    refund_count: Int as uint32;        // 退款次数
    refunded_amount_ton: Int as coins;  // TON 退款总额
    refunded_amount_usdt: Int as coins; // USDT 退款总额
}
```

## 主要查询方法

### 1. 获取特定轮次信息
```typescript
// 获取第3轮的统计信息
const round3Stats = await onionAuction.getRoundStats(3n);
if (round3Stats) {
    console.log(`第3轮价格: ${round3Stats.price}`);
    console.log(`TON筹集: ${round3Stats.total_raised_ton}`);
    console.log(`USDT筹集: ${round3Stats.total_raised_usdt}`);
    console.log(`参与用户: ${round3Stats.unique_users}`);
    console.log(`购买次数: ${round3Stats.purchase_count}`);
}
```

### 2. 获取当前轮次信息
```typescript
// 获取当前活跃轮次的统计信息
const currentRoundStats = await onionAuction.getCurrentRoundStats();
if (currentRoundStats) {
    console.log(`当前轮次: ${currentRoundStats.round_number}`);
    console.log(`当前价格: ${currentRoundStats.price}`);
    console.log(`本轮筹集: ${currentRoundStats.total_raised_ton + currentRoundStats.total_raised_usdt}`);
}
```

### 3. 获取总轮次数
```typescript
// 获取到目前为止的总轮次数
const totalRounds = await onionAuction.getTotalRounds();
console.log(`总轮次数: ${totalRounds}`);
```

### 4. 获取所有轮次汇总
```typescript
// 获取所有轮次的汇总信息
const allRoundsSummary = await onionAuction.getAllRoundsSummary();
for (let i = 1; i <= totalRounds; i++) {
    const roundStats = allRoundsSummary.get(i);
    if (roundStats) {
        console.log(`轮次 ${i}: 价格 ${roundStats.price}, 筹集 ${roundStats.total_raised_ton}`);
    }
}
```

### 5. 获取聚合统计
```typescript
// 获取所有轮次的聚合统计信息
const aggregatedStats = await onionAuction.getAggregatedStats();
console.log(`总筹集TON: ${aggregatedStats.total_raised_ton}`);
console.log(`总筹集USDT: ${aggregatedStats.total_raised_usdt}`);
console.log(`总代币销售: ${aggregatedStats.tokens_sold}`);
console.log(`总购买次数: ${aggregatedStats.purchase_count}`);
console.log(`总退款次数: ${aggregatedStats.refund_count}`);
```

## 用户相关查询

### 1. 查询用户参与的轮次
```typescript
// 获取用户参与的所有轮次
const userRounds = await onionAuction.getUserParticipatedRounds(userAddress);
if (userRounds) {
    console.log("用户参与的轮次:");
    // 遍历 map 查看用户参与了哪些轮次
}
```

### 2. 查询用户参与轮次数量
```typescript
// 获取用户参与的轮次总数
const userRoundCount = await onionAuction.getUserRoundCount(userAddress);
console.log(`用户参与了 ${userRoundCount} 个轮次`);
```

### 3. 查询轮次参与者
```typescript
// 获取第5轮的所有参与者
const round5Participants = await onionAuction.getRoundParticipants(5n);
if (round5Participants) {
    console.log("第5轮参与者列表:");
    // 遍历 map 查看参与者地址
}
```

## 用户购买合约查询

### 1. 按轮次查询购买记录
```typescript
// 获取用户在第2轮的所有购买记录
const userPurchase = await UserPurchase.fromInit(auctionAddress, userAddress);
const round2Purchases = await userPurchase.getPurchasesByRound(2n);
```

### 2. 查询用户参与的轮次
```typescript
// 获取用户参与的所有轮次
const participatedRounds = await userPurchase.getParticipatedRounds();
const roundCount = await userPurchase.getRoundCount();
console.log(`用户参与了 ${roundCount} 个轮次`);
```

### 3. 按轮次查询统计
```typescript
// 获取用户在特定轮次的统计信息
const round3Amount = await userPurchase.getRoundTotalAmount(3n);
const round3Tokens = await userPurchase.getRoundTotalTokens(3n);
const round3Count = await userPurchase.getRoundPurchaseCount(3n);

console.log(`第3轮投资: ${round3Amount}`);
console.log(`第3轮获得代币: ${round3Tokens}`);
console.log(`第3轮购买次数: ${round3Count}`);
```

## 实时监控示例

### 监控当前轮次变化
```typescript
async function monitorCurrentRound() {
    let lastRound = 0;
    
    setInterval(async () => {
        const currentRound = await onionAuction.getCurrentRound();
        
        if (currentRound > lastRound) {
            console.log(`新轮次开始: ${currentRound}`);
            
            // 获取新轮次信息
            const roundStats = await onionAuction.getCurrentRoundStats();
            if (roundStats) {
                console.log(`新轮次价格: ${roundStats.price}`);
            }
            
            // 如果有上一轮，显示上一轮总结
            if (lastRound > 0) {
                const lastRoundStats = await onionAuction.getRoundStats(lastRound);
                if (lastRoundStats) {
                    console.log(`第${lastRound}轮结束:`);
                    console.log(`- 筹集: ${lastRoundStats.total_raised_ton} TON`);
                    console.log(`- 参与者: ${lastRoundStats.unique_users} 人`);
                    console.log(`- 购买次数: ${lastRoundStats.purchase_count}`);
                }
            }
            
            lastRound = currentRound;
        }
    }, 10000); // 每10秒检查一次
}
```

## 数据分析示例

### 计算平均每轮筹集金额
```typescript
async function calculateAverageRaised() {
    const totalRounds = await onionAuction.getTotalRounds();
    const aggregatedStats = await onionAuction.getAggregatedStats();
    
    const avgTonPerRound = aggregatedStats.total_raised_ton / totalRounds;
    const avgUsdtPerRound = aggregatedStats.total_raised_usdt / totalRounds;
    
    console.log(`平均每轮筹集 TON: ${avgTonPerRound}`);
    console.log(`平均每轮筹集 USDT: ${avgUsdtPerRound}`);
}
```

### 分析轮次趋势
```typescript
async function analyzeRoundTrends() {
    const totalRounds = await onionAuction.getTotalRounds();
    const allRounds = await onionAuction.getAllRoundsSummary();
    
    console.log("轮次趋势分析:");
    for (let i = 1; i <= totalRounds; i++) {
        const roundStats = allRounds.get(i);
        if (roundStats) {
            const totalRaised = roundStats.total_raised_ton + roundStats.total_raised_usdt;
            console.log(`轮次 ${i}: 价格 ${roundStats.price}, 筹集 ${totalRaised}, 用户 ${roundStats.unique_users}`);
        }
    }
}
```

## 注意事项

1. **轮次时间**: 每轮持续1小时（3600秒），价格每轮递增0.01 TON
2. **实时更新**: 当前轮次的统计信息会实时更新，直到轮次结束
3. **用户跟踪**: 系统会跟踪每个用户在每轮的参与情况，避免重复计算
4. **退款影响**: 退款会更新对应轮次的退款统计，但不会减少参与用户数
5. **Gas 优化**: 查询操作不消耗 gas，但合约状态更新需要 gas

这个轮次跟踪系统为拍卖提供了完整的数据分析能力，可以帮助了解每轮的表现和整体趋势。
