import { Address } from '@ton/core';
import { PurchaseCalculation, AuctionState } from './types';
import { auctionConfig } from './config';

export class TokenCalculator {
  private nonce_counter: bigint = BigInt(1);

  /**
   * Calculate current auction round based on time
   */
  calculateCurrentRound(current_time: number): number {
    const elapsed_time = current_time - auctionConfig.start_time;
    return Math.max(1, Math.floor(elapsed_time / auctionConfig.round_duration) + 1);
  }

  /**
   * Calculate current token price based on round
   */
  calculateCurrentPrice(current_round: number): bigint {
    const round_increment = BigInt(current_round - 1);
    return auctionConfig.initial_price + (auctionConfig.price_increment * round_increment);
  }

  /**
   * Calculate tokens to receive for given amount
   */
  calculateTokensToReceive(amount: bigint, current_price: bigint): bigint {
    // Formula: tokens = (amount * 10^9) / current_price
    const TON_DECIMALS = BigInt(1000000000); // 10^9
    return (amount * TON_DECIMALS) / current_price;
  }

  /**
   * Validate purchase parameters
   */
  validatePurchase(amount: bigint, currency: number, current_time: number, tokens_to_receive?: bigint): { valid: boolean; error?: string } {
    // Check auction time window
    if (current_time < auctionConfig.start_time) {
      return { valid: false, error: 'Auction not started' };
    }

    if (current_time > auctionConfig.end_time) {
      return { valid: false, error: 'Auction ended' };
    }

    // Check minimum purchase amount
    let effective_amount = amount;
    if (currency === 1) { // USDT
      // Convert USDT (6 decimals) to TON equivalent (9 decimals)
      effective_amount = amount * BigInt(1000);
    }

    if (effective_amount < auctionConfig.min_purchase) {
      return { valid: false, error: 'Amount below minimum purchase' };
    }

    // Check if purchase would exceed hard cap (if tokens_to_receive is provided)
    if (tokens_to_receive !== undefined) {
      const currentState = this.getCurrentAuctionState();
      const totalAfterPurchase = currentState.total_tokens_sold + tokens_to_receive;

      if (totalAfterPurchase > auctionConfig.hard_cap) {
        return { valid: false, error: 'Purchase would exceed hard cap' };
      }

      // Check if purchase would exceed current round's soft cap
      const currentRoundAfterPurchase = currentState.current_round_tokens_sold + tokens_to_receive;
      if (currentRoundAfterPurchase > auctionConfig.soft_cap) {
        return { valid: false, error: 'Purchase would exceed round soft cap' };
      }
    }

    return { valid: true };
  }

  /**
   * Generate purchase calculation with signature data
   */
  generatePurchaseCalculation(
    user_address: string,
    amount: bigint,
    currency: number
  ): PurchaseCalculation {
    const current_time = Math.floor(Date.now() / 1000);

    // Calculate current round and price
    const current_round = this.calculateCurrentRound(current_time);
    const current_price = this.calculateCurrentPrice(current_round);

    // Calculate effective amount for token calculation
    let effective_amount = amount;
    if (currency === 1) { // USDT
      effective_amount = amount * BigInt(1000); // Convert to TON equivalent
    }

    // Calculate tokens to receive
    const tokens_to_receive = this.calculateTokensToReceive(effective_amount, current_price);

    // Validate purchase (including hard cap check)
    const validation = this.validatePurchase(amount, currency, current_time, tokens_to_receive);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // Calculate USDT equivalent amount for cap checking
    let usdt_equivalent_amount = amount;
    if (currency === 0) { // TON
      // For TON purchases, convert to USDT equivalent (assuming 1 TON = 1 USDT for simplicity)
      // In production, this should use real exchange rates
      usdt_equivalent_amount = amount;
    }
    // For USDT purchases, usdt_equivalent_amount is the same as amount

    // Generate unique nonce
    const nonce = this.nonce_counter++;

    return {
      $$type: 'PurchaseCalculation' as const,
      user: Address.parse(user_address),
      amount,
      currency: BigInt(currency),
      tokens_to_receive,
      current_price,
      current_round: BigInt(current_round),
      timestamp: BigInt(current_time),
      nonce: BigInt(nonce),
      usdt_equivalent_amount
    };
  }

  /**
   * Get current auction state (for display purposes)
   */
  getCurrentAuctionState(): AuctionState {
    const current_time = Math.floor(Date.now() / 1000);
    const current_round = this.calculateCurrentRound(current_time);
    const current_price = this.calculateCurrentPrice(current_round);

    // Mock state for demo - in real implementation, this would come from the contract
    return {
      current_round,
      current_price,
      total_raised: BigInt('1600000000000000'), // 1.6M TON
      total_tokens_sold: BigInt('673000000000000'), // 673k tokens
      current_round_tokens_sold: BigInt('100000000000000'), // 100k tokens sold in current round (mock)
      auction_status: 1, // Active
      purchase_count: 1250
    };
  }

  /**
   * Reset nonce counter (for testing purposes)
   */
  resetNonce(): void {
    this.nonce_counter = BigInt(1);
  }
}
