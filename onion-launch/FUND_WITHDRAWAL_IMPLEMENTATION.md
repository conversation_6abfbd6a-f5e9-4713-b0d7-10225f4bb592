# 资金提取功能实现总结

## 🎯 功能概述

为 OnionAuction 合约添加了完整的资金提取功能，允许合约所有者在拍卖成功结束后安全地将筹集到的 TON 和 USDT 提取到指定钱包地址。

## 🏗️ 核心实现

### 1. 新增错误码
```tact
const ERROR_WITHDRAWAL_NOT_ALLOWED: Int = 551019;  // 不允许提取
const ERROR_INSUFFICIENT_BALANCE: Int = 551020;    // 余额不足
const ERROR_INVALID_WITHDRAWAL_ADDRESS: Int = 551021; // 无效地址
```

### 2. 新增操作码
```tact
const OP_WITHDRAW_TON: Int = 0xd1d1d1d1;    // 提取TON
const OP_WITHDRAW_USDT: Int = 0xd2d2d2d2;   // 提取USDT
const OP_SET_TREASURY: Int = 0xd3d3d3d3;    // 设置财库
```

### 3. 新增消息类型
```tact
// 设置财库地址
message(OP_SET_TREASURY) SetTreasury {
    treasury_address: Address;
}

// 提取TON
message(OP_WITHDRAW_TON) WithdrawTON {
    amount: Int as coins;      // 提取金额 (0=全部)
    destination: Address;      // 目标地址
}

// 提取USDT
message(OP_WITHDRAW_USDT) WithdrawUSDT {
    amount: Int as coins;      // 提取金额 (0=全部)
    destination: Address;      // 目标地址
}
```

### 4. 新增状态变量
```tact
// 财库管理
treasury_address: Address?; // 财库钱包地址
```

## 🔐 安全机制

### 1. 权限控制
```tact
// 所有提取操作都需要所有者权限
receive(msg: WithdrawTON) {
    self.requireOwner(); // 仅限所有者
    // ...
}
```

### 2. 状态验证
```tact
// 只有拍卖成功结束后才能提取
throwUnless(ERROR_WITHDRAWAL_NOT_ALLOWED, self.auction_status == 2);
```

### 3. 余额检查
```tact
// 确保提取金额不超过可用余额
throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount <= self.total_raised);
throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount > 0);
```

## 💰 提取逻辑

### 1. TON 提取实现
```tact
receive(msg: WithdrawTON) {
    self.requireOwner();
    throwUnless(ERROR_WITHDRAWAL_NOT_ALLOWED, self.auction_status == 2);
    
    let withdrawal_amount: Int = msg.amount;
    if (withdrawal_amount == 0) {
        withdrawal_amount = self.total_raised; // 提取全部
    }
    
    throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount <= self.total_raised);
    
    // 更新余额
    self.total_raised -= withdrawal_amount;
    
    // 发送TON
    send(SendParameters{
        to: msg.destination,
        value: withdrawal_amount,
        mode: SendIgnoreErrors,
        bounce: false,
        body: "TON withdrawal from OnionAuction".asComment()
    });
}
```

### 2. USDT 提取实现
```tact
receive(msg: WithdrawUSDT) {
    self.requireOwner();
    throwUnless(ERROR_WITHDRAWAL_NOT_ALLOWED, self.auction_status == 2);
    throwUnless(ERROR_USDT_NOT_CONFIGURED, self.usdt_config != null);
    
    let usdt_config: USDTConfig = self.usdt_config!!;
    throwUnless(ERROR_USDT_NOT_CONFIGURED, usdt_config.wallet_address != null);
    
    let withdrawal_amount: Int = msg.amount;
    if (withdrawal_amount == 0) {
        withdrawal_amount = self.total_raised_usdt; // 提取全部
    }
    
    throwUnless(ERROR_INSUFFICIENT_BALANCE, withdrawal_amount <= self.total_raised_usdt);
    
    // 更新余额
    self.total_raised_usdt -= withdrawal_amount;
    
    // 通过Jetton转账发送USDT
    send(SendParameters{
        to: usdt_config.wallet_address!!,
        value: ton("0.1"),
        mode: SendIgnoreErrors,
        bounce: false,
        body: JettonTransfer{
            query_id: 0,
            amount: withdrawal_amount,
            destination: msg.destination,
            response_destination: myAddress(),
            custom_payload: null,
            forward_ton_amount: 1,
            forward_payload: "USDT withdrawal from OnionAuction".asComment()
        }.toCell()
    });
}
```

## 📊 查询接口

### 1. 基础查询方法
```tact
// 财库地址
get fun treasury_address(): Address?

// 可提取金额
get fun withdrawable_ton(): Int
get fun withdrawable_usdt(): Int

// 提取权限检查
get fun can_withdraw(): Bool
```

### 2. 汇总查询方法
```tact
// 提取状态摘要 (使用数字键)
get fun withdrawal_summary(): map<Int, Int> {
    let summary: map<Int, Int> = emptyMap();
    summary.set(1, self.auction_status);        // 拍卖状态
    summary.set(2, self.withdrawable_ton());    // 可提取TON
    summary.set(3, self.withdrawable_usdt());   // 可提取USDT
    return summary;
}
```

## 🔧 TypeScript 集成

### 1. 前端包装器方法
```typescript
// 设置财库地址
async sendSetTreasury(provider: ContractProvider, via: Sender, value: bigint, treasuryAddress: Address)

// 提取TON
async sendWithdrawTON(provider: ContractProvider, via: Sender, value: bigint, amount: bigint, destination: Address)

// 提取USDT
async sendWithdrawUSDT(provider: ContractProvider, via: Sender, value: bigint, amount: bigint, destination: Address)

// 查询方法
async getTreasuryAddress(provider: ContractProvider): Promise<Address | null>
async getWithdrawableTON(provider: ContractProvider): Promise<bigint>
async getWithdrawableUSDT(provider: ContractProvider): Promise<bigint>
async canWithdraw(provider: ContractProvider): Promise<boolean>
```

### 2. 使用示例
```typescript
// 检查提取条件
const canWithdraw = await onionAuction.canWithdraw();
if (!canWithdraw) {
    console.log('拍卖尚未成功结束');
    return;
}

// 查询可提取金额
const withdrawableTON = await onionAuction.getWithdrawableTON();
const withdrawableUSDT = await onionAuction.getWithdrawableUSDT();

// 提取所有资金
if (withdrawableTON > 0n) {
    await onionAuction.sendWithdrawTON(
        provider,
        owner.getSender(),
        toNano('0.1'),
        0n, // 0 = 提取全部
        treasuryAddress
    );
}
```

## 🧪 测试覆盖

### 1. 功能测试
- ✅ 设置财库地址
- ✅ 提取TON资金（部分/全部）
- ✅ 提取USDT资金（部分/全部）
- ✅ 权限验证（仅限所有者）
- ✅ 状态验证（仅限成功拍卖）
- ✅ 余额验证（不超过可用金额）

### 2. 边界测试
- ✅ 拍卖未结束时禁止提取
- ✅ 非所有者无法提取
- ✅ 提取金额超过余额时失败
- ✅ USDT未配置时无法提取USDT

### 3. 集成测试
- ✅ 完整的拍卖-提取流程
- ✅ 多次分批提取
- ✅ 混合TON和USDT提取

## 📋 部署脚本

### 1. 资金提取脚本
```bash
# 使用交互式脚本提取资金
npx blueprint run withdrawFunds --custom <contract_address> <treasury_address>
```

### 2. 脚本功能
- 🔍 自动检查拍卖状态
- 📊 显示资金摘要
- 🏦 设置/更新财库地址
- 💰 交互式提取操作
- ✅ 提取结果验证

## ⚡ Gas 费用

| 操作 | 预估Gas费用 | 说明 |
|------|-------------|------|
| 设置财库地址 | ~0.05 TON | 一次性设置 |
| 提取TON | ~0.1 TON | 直接转账 |
| 提取USDT | ~0.1 TON | 包含Jetton转账费用 |

## 🔒 安全考虑

### 1. 权限管理
- 只有合约所有者可以执行提取操作
- 所有提取操作都有明确的权限检查

### 2. 状态验证
- 只有拍卖成功结束后才能提取资金
- 防止在拍卖进行中提取资金

### 3. 余额保护
- 严格的余额检查防止过度提取
- 支持部分提取和全额提取

### 4. 地址验证
- 财库地址可以预先设置和验证
- 支持灵活的目标地址指定

## 🚀 扩展性

### 1. 未来增强
- 支持多签名提取
- 添加提取时间锁
- 实现自动化提取触发器

### 2. 兼容性
- 向后兼容现有功能
- 不影响拍卖核心逻辑
- 支持渐进式部署

这个资金提取系统为 OnionAuction 提供了安全、灵活、易用的资金管理能力，确保拍卖成功后能够顺利、安全地提取筹集到的资金。
